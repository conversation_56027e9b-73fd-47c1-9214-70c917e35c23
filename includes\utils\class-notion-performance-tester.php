<?php
declare(strict_types=1);

/**
 * Notion 性能测试器类
 * 
 * 用于测试和验证性能优化效果
 * 
 * @since      2.0.0-beta.1
 * @version    2.0.0-beta.1
 * @package    Notion_To_WordPress
 * <AUTHOR>
 * @license    GPL-3.0-or-later
 * @link       https://github.com/Frank-Loong/Notion-to-WordPress
 */

// 如果直接访问此文件，则退出
if (!defined('ABSPATH')) {
    exit;
}

class Notion_Performance_Tester {
    
    /**
     * 测试配置参数优化效果
     *
     * @since 2.0.0-beta.1
     * @return array 测试结果
     */
    public static function test_config_optimization(): array {
        $results = [
            'stream_processor' => [],
            'memory_manager' => [],
            'logger' => []
        ];
        
        // 测试流处理器配置
        if (class_exists('Notion_Stream_Processor')) {
            $results['stream_processor'] = [
                'chunk_size' => Notion_Stream_Processor::DEFAULT_CHUNK_SIZE,
                'memory_check_frequency' => Notion_Stream_Processor::MEMORY_CHECK_FREQUENCY,
                'gc_threshold' => Notion_Stream_Processor::GC_TRIGGER_THRESHOLD,
                'optimized' => true
            ];
        }
        
        // 测试内存管理器配置
        if (class_exists('Notion_Memory_Manager')) {
            $results['memory_manager'] = [
                'gc_frequency' => Notion_Memory_Manager::GC_FREQUENCY,
                'optimized' => true
            ];
        }
        
        // 测试日志器性能模式
        if (class_exists('Notion_Logger')) {
            $results['logger'] = [
                'performance_mode' => Notion_Logger::is_performance_mode(),
                'optimized' => true
            ];
        }
        
        return $results;
    }
    
    /**
     * 测试数组操作优化效果
     *
     * @since 2.0.0-beta.1
     * @return array 测试结果
     */
    public static function test_array_optimization(): array {
        $test_data = range(1, 1000);
        $chunks = array_chunk($test_data, 100);
        
        // 测试优化前的array_merge
        $start_time = microtime(true);
        $start_memory = memory_get_usage();
        
        $results_old = [];
        foreach ($chunks as $chunk) {
            $results_old = array_merge($results_old, $chunk);
        }
        
        $old_time = microtime(true) - $start_time;
        $old_memory = memory_get_usage() - $start_memory;
        
        // 测试优化后的数组追加
        $start_time = microtime(true);
        $start_memory = memory_get_usage();
        
        $results_new = [];
        foreach ($chunks as $chunk) {
            foreach ($chunk as $item) {
                $results_new[] = $item;
            }
        }
        
        $new_time = microtime(true) - $start_time;
        $new_memory = memory_get_usage() - $start_memory;
        
        return [
            'old_method' => [
                'time' => $old_time,
                'memory' => $old_memory,
                'count' => count($results_old)
            ],
            'new_method' => [
                'time' => $new_time,
                'memory' => $new_memory,
                'count' => count($results_new)
            ],
            'improvement' => [
                'time_saved_percent' => round((($old_time - $new_time) / $old_time) * 100, 2),
                'memory_saved_percent' => round((($old_memory - $new_memory) / $old_memory) * 100, 2)
            ]
        ];
    }
    
    /**
     * 测试生成器内存优化效果
     *
     * @since 2.0.0-beta.1
     * @return array 测试结果
     */
    public static function test_generator_optimization(): array {
        if (!class_exists('Notion_Memory_Manager')) {
            return ['error' => 'Notion_Memory_Manager class not found'];
        }
        
        $test_data = range(1, 1000);
        $processor = function($chunk) {
            return array_map(function($item) { return $item * 2; }, $chunk);
        };
        
        // 测试传统方法
        $start_time = microtime(true);
        $start_memory = memory_get_usage();
        
        $traditional_results = [];
        $chunks = array_chunk($test_data, 30);
        foreach ($chunks as $chunk) {
            $chunk_result = $processor($chunk);
            foreach ($chunk_result as $result) {
                $traditional_results[] = $result;
            }
        }
        
        $traditional_time = microtime(true) - $start_time;
        $traditional_memory = memory_get_peak_usage() - $start_memory;
        
        // 测试生成器方法
        $start_time = microtime(true);
        $start_memory = memory_get_usage();
        
        $generator_results = Notion_Memory_Manager::batch_process_large_dataset($test_data, $processor, 30);
        
        $generator_time = microtime(true) - $start_time;
        $generator_memory = memory_get_peak_usage() - $start_memory;
        
        return [
            'traditional' => [
                'time' => $traditional_time,
                'memory' => $traditional_memory,
                'count' => count($traditional_results)
            ],
            'generator' => [
                'time' => $generator_time,
                'memory' => $generator_memory,
                'count' => count($generator_results)
            ],
            'improvement' => [
                'time_saved_percent' => round((($traditional_time - $generator_time) / $traditional_time) * 100, 2),
                'memory_saved_percent' => round((($traditional_memory - $generator_memory) / $traditional_memory) * 100, 2)
            ]
        ];
    }
    
    /**
     * 测试API去重优化效果
     *
     * @since 2.0.0-beta.1
     * @return array 测试结果
     */
    public static function test_api_deduplication(): array {
        // 创建测试请求数据
        $test_requests = [];
        for ($i = 0; $i < 100; $i++) {
            $test_requests[] = [
                'method' => 'GET',
                'endpoint' => '/databases/' . ($i % 10), // 创建重复
                'data' => ['param' => $i % 5] // 创建重复
            ];
        }
        
        // 测试传统O(n²)去重
        $start_time = microtime(true);
        $traditional_unique = [];
        foreach ($test_requests as $request) {
            $is_duplicate = false;
            foreach ($traditional_unique as $existing) {
                if (serialize($request) === serialize($existing)) {
                    $is_duplicate = true;
                    break;
                }
            }
            if (!$is_duplicate) {
                $traditional_unique[] = $request;
            }
        }
        $traditional_time = microtime(true) - $start_time;
        
        // 测试优化的哈希表去重
        $start_time = microtime(true);
        $seen = [];
        $optimized_unique = [];
        foreach ($test_requests as $request) {
            $key = md5(serialize($request));
            if (!isset($seen[$key])) {
                $seen[$key] = true;
                $optimized_unique[] = $request;
            }
        }
        $optimized_time = microtime(true) - $start_time;
        
        return [
            'original_count' => count($test_requests),
            'traditional' => [
                'time' => $traditional_time,
                'unique_count' => count($traditional_unique)
            ],
            'optimized' => [
                'time' => $optimized_time,
                'unique_count' => count($optimized_unique)
            ],
            'improvement' => [
                'time_saved_percent' => round((($traditional_time - $optimized_time) / $traditional_time) * 100, 2),
                'speed_multiplier' => round($traditional_time / $optimized_time, 2)
            ]
        ];
    }
    
    /**
     * 运行所有性能测试
     *
     * @since 2.0.0-beta.1
     * @return array 完整测试结果
     */
    public static function run_all_tests(): array {
        $start_time = microtime(true);
        
        $results = [
            'timestamp' => current_time('mysql'),
            'config_optimization' => self::test_config_optimization(),
            'array_optimization' => self::test_array_optimization(),
            'generator_optimization' => self::test_generator_optimization(),
            'api_deduplication' => self::test_api_deduplication(),
            'total_test_time' => 0
        ];
        
        $results['total_test_time'] = microtime(true) - $start_time;
        
        if (class_exists('Notion_Logger')) {
            Notion_Logger::info_log(
                '性能测试完成: ' . json_encode($results, JSON_PRETTY_PRINT),
                'Performance Tester'
            );
        }
        
        return $results;
    }
}
