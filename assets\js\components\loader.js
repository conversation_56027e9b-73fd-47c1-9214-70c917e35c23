/**
 * Notion to WordPress - 组件加载器
 * 确保所有组件和依赖按正确顺序加载
 */

(function() {
    'use strict';

    // 组件加载配置
    const COMPONENT_CONFIG = {
        // 基础依赖（必须首先加载）
        dependencies: [
            'services/notion-event-bus.js',
            'services/notion-api-service.js',
            'components/base-component.js',
            'components/component-registry.js'
        ],
        
        // 核心组件（按依赖顺序）
        components: [
            'components/tab-manager.js',
            'components/api-settings.js',
            'components/field-mapping.js',
            'components/other-settings.js',
            'components/performance-monitor.js',
            'components/debug-tools.js',
            'components/help-docs.js',
            'components/about-author.js'
        ],
        
        // 应用入口（最后加载）
        application: [
            'components/app.js',
            'components/init.js'
        ]
    };

    // 加载状态管理
    const LoaderState = {
        loaded: new Set(),
        failed: new Set(),
        loading: new Set(),
        callbacks: new Map()
    };

    /**
     * 组件加载器类
     */
    class NotionComponentLoader {
        constructor() {
            this.baseUrl = this.getBaseUrl();
            this.loadPromises = new Map();
            this.retryCount = new Map();
            this.maxRetries = 3;
            this.loadTimeout = 10000; // 10秒超时
        }

        /**
         * 获取基础URL
         */
        getBaseUrl() {
            // 尝试从全局配置获取
            if (window.notionToWp && window.notionToWp.plugin_url) {
                return window.notionToWp.plugin_url + 'assets/js/';
            }

            // 从当前脚本路径推断
            const scripts = document.querySelectorAll('script[src*="notion-to-wordpress"]');
            if (scripts.length > 0) {
                const scriptSrc = scripts[scripts.length - 1].src;
                const match = scriptSrc.match(/(.+\/assets\/js\/)/);
                if (match) {
                    return match[1];
                }
            }

            // 默认路径
            return '/wp-content/plugins/notion-to-wordpress/assets/js/';
        }

        /**
         * 加载单个脚本
         */
        async loadScript(path) {
            const fullPath = this.baseUrl + path;
            
            // 检查是否已经加载
            if (LoaderState.loaded.has(path)) {
                return Promise.resolve();
            }

            // 检查是否正在加载
            if (this.loadPromises.has(path)) {
                return this.loadPromises.get(path);
            }

            // 创建加载Promise
            const loadPromise = new Promise((resolve, reject) => {
                const script = document.createElement('script');
                script.src = fullPath;
                script.async = true;
                script.defer = true;

                // 设置超时
                const timeout = setTimeout(() => {
                    reject(new Error(`Script load timeout: ${path}`));
                }, this.loadTimeout);

                script.onload = () => {
                    clearTimeout(timeout);
                    LoaderState.loaded.add(path);
                    LoaderState.loading.delete(path);
                    console.log(`✓ Loaded: ${path}`);
                    resolve();
                };

                script.onerror = () => {
                    clearTimeout(timeout);
                    LoaderState.failed.add(path);
                    LoaderState.loading.delete(path);
                    
                    const retries = this.retryCount.get(path) || 0;
                    if (retries < this.maxRetries) {
                        console.warn(`⚠ Failed to load ${path}, retrying... (${retries + 1}/${this.maxRetries})`);
                        this.retryCount.set(path, retries + 1);
                        
                        // 重试
                        setTimeout(() => {
                            this.loadPromises.delete(path);
                            this.loadScript(path).then(resolve).catch(reject);
                        }, 1000 * (retries + 1)); // 递增延迟
                    } else {
                        console.error(`✗ Failed to load: ${path}`);
                        reject(new Error(`Failed to load script: ${path}`));
                    }
                };

                LoaderState.loading.add(path);
                document.head.appendChild(script);
            });

            this.loadPromises.set(path, loadPromise);
            return loadPromise;
        }

        /**
         * 按顺序加载脚本组
         */
        async loadScriptGroup(scripts, groupName) {
            console.log(`Loading ${groupName}...`);
            
            const results = [];
            for (const script of scripts) {
                try {
                    await this.loadScript(script);
                    results.push({ script, success: true });
                } catch (error) {
                    console.error(`Failed to load ${script}:`, error);
                    results.push({ script, success: false, error });
                }
            }

            const successful = results.filter(r => r.success).length;
            const failed = results.filter(r => !r.success).length;
            
            console.log(`${groupName} loading complete: ${successful} successful, ${failed} failed`);
            
            if (failed > 0) {
                const failedScripts = results.filter(r => !r.success).map(r => r.script);
                throw new Error(`Failed to load ${groupName}: ${failedScripts.join(', ')}`);
            }
            
            return results;
        }

        /**
         * 加载所有组件
         */
        async loadAll() {
            try {
                console.log('🚀 Starting Notion Components loading...');
                
                // 1. 加载基础依赖
                await this.loadScriptGroup(COMPONENT_CONFIG.dependencies, 'Dependencies');
                
                // 等待基础组件就绪
                await this.waitForDependencies();
                
                // 2. 加载核心组件
                await this.loadScriptGroup(COMPONENT_CONFIG.components, 'Components');
                
                // 3. 加载应用入口
                await this.loadScriptGroup(COMPONENT_CONFIG.application, 'Application');
                
                console.log('✅ All Notion Components loaded successfully');
                
                // 触发加载完成事件
                this.dispatchLoadEvent('notion:components:loaded', {
                    loaded: Array.from(LoaderState.loaded),
                    failed: Array.from(LoaderState.failed)
                });
                
                return true;
                
            } catch (error) {
                console.error('❌ Failed to load Notion Components:', error);
                
                // 触发加载失败事件
                this.dispatchLoadEvent('notion:components:failed', {
                    error: error.message,
                    loaded: Array.from(LoaderState.loaded),
                    failed: Array.from(LoaderState.failed)
                });
                
                throw error;
            }
        }

        /**
         * 等待基础依赖就绪
         */
        async waitForDependencies() {
            const checkDependencies = () => {
                return window.NotionBaseComponent && 
                       window.NotionRegistry && 
                       window.NotionEventBus &&
                       window.NotionApiService;
            };

            if (checkDependencies()) {
                return Promise.resolve();
            }

            return new Promise((resolve, reject) => {
                let attempts = 0;
                const maxAttempts = 50; // 5秒超时
                
                const check = () => {
                    attempts++;
                    
                    if (checkDependencies()) {
                        console.log('✓ Base dependencies ready');
                        resolve();
                    } else if (attempts >= maxAttempts) {
                        reject(new Error('Timeout waiting for base dependencies'));
                    } else {
                        setTimeout(check, 100);
                    }
                };
                
                check();
            });
        }

        /**
         * 触发加载事件
         */
        dispatchLoadEvent(eventName, detail) {
            const event = new CustomEvent(eventName, {
                detail,
                bubbles: true
            });
            document.dispatchEvent(event);
        }

        /**
         * 获取加载状态
         */
        getLoadStatus() {
            return {
                loaded: Array.from(LoaderState.loaded),
                failed: Array.from(LoaderState.failed),
                loading: Array.from(LoaderState.loading),
                totalScripts: COMPONENT_CONFIG.dependencies.length + 
                             COMPONENT_CONFIG.components.length + 
                             COMPONENT_CONFIG.application.length
            };
        }

        /**
         * 重新加载失败的脚本
         */
        async retryFailed() {
            const failed = Array.from(LoaderState.failed);
            if (failed.length === 0) {
                console.log('No failed scripts to retry');
                return;
            }

            console.log(`Retrying ${failed.length} failed scripts...`);
            
            // 清除失败状态
            failed.forEach(script => {
                LoaderState.failed.delete(script);
                this.retryCount.delete(script);
                this.loadPromises.delete(script);
            });

            // 重新加载
            const results = [];
            for (const script of failed) {
                try {
                    await this.loadScript(script);
                    results.push({ script, success: true });
                } catch (error) {
                    results.push({ script, success: false, error });
                }
            }

            const successful = results.filter(r => r.success).length;
            console.log(`Retry complete: ${successful}/${failed.length} successful`);
            
            return results;
        }
    }

    // 创建全局加载器实例
    window.NotionComponentLoader = new NotionComponentLoader();

    // 提供调试接口
    window.NotionLoaderDebug = {
        getStatus: () => window.NotionComponentLoader.getLoadStatus(),
        retryFailed: () => window.NotionComponentLoader.retryFailed(),
        loadAll: () => window.NotionComponentLoader.loadAll()
    };

    // 检查是否应该自动加载
    const shouldAutoLoad = () => {
        // 检查是否在管理页面
        return document.querySelector('.notion-wp-admin-page') !== null ||
               document.querySelector('#notion-to-wordpress-plugin-admin') !== null ||
               window.location.href.includes('notion-to-wordpress');
    };

    // 自动加载（如果在正确的页面）
    if (shouldAutoLoad()) {
        // 等待DOM就绪后开始加载
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                window.NotionComponentLoader.loadAll().catch(console.error);
            });
        } else {
            window.NotionComponentLoader.loadAll().catch(console.error);
        }
    } else {
        console.log('Notion Component Loader: Not on admin page, skipping auto-load');
    }

    // 在控制台显示加载器信息
    console.log('%c📦 Notion Component Loader Ready', 'color: #7c3aed; font-weight: bold; font-size: 14px;');
    console.log('%cUse NotionLoaderDebug for debugging tools', 'color: #6b7280; font-size: 12px;');

})();
