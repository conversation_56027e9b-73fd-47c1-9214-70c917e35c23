/**
 * Notion to WordPress - 基础组件类
 * 使用现代Web Components技术重构后台界面
 */

class NotionBaseComponent extends HTMLElement {
    constructor() {
        super();

        // 组件状态管理
        this.state = {};
        this.previousState = {};

        // 事件管理
        this.eventListeners = new Map();
        this.eventBusListeners = new Map(); // 事件总线监听器管理

        // 生命周期状态
        this.initialized = false;
        this.mounted = false;
        this.destroyed = false;

        // 服务注入系统
        this.services = {
            api: null, // 延迟初始化，避免循环依赖
            eventBus: null
        };

        // 组件配置
        this.config = {
            autoRender: true, // 状态变化时自动重新渲染
            debugMode: false, // 调试模式
            errorBoundary: true // 错误边界
        };

        // 性能监控
        this.performanceMetrics = {
            initTime: 0,
            renderTime: 0,
            mountTime: 0
        };

        // 初始化服务（延迟加载）
        this.initializeServices();
    }

    /**
     * 初始化服务系统
     * 延迟加载避免循环依赖
     */
    initializeServices() {
        // 延迟初始化，确保全局对象已加载
        setTimeout(() => {
            try {
                if (window.NotionApiService) {
                    this.services.api = new window.NotionApiService();
                }
                if (window.NotionEventBus) {
                    this.services.eventBus = window.NotionEventBus.getInstance();
                }
            } catch (error) {
                console.warn('NotionBaseComponent: 服务初始化失败', error);
            }
        }, 0);
    }

    /**
     * 服务注入方法
     * 允许外部注入自定义服务
     */
    injectService(name, service) {
        if (typeof name !== 'string' || !service) {
            throw new Error('Invalid service injection parameters');
        }

        this.services[name] = service;

        if (this.config.debugMode) {
            console.log(`NotionBaseComponent: 注入服务 "${name}"`);
        }

        return this;
    }

    /**
     * 获取服务实例
     */
    getService(name) {
        return this.services[name] || null;
    }

    /**
     * 组件连接到DOM时调用
     */
    connectedCallback() {
        if (this.destroyed) {
            console.warn('NotionBaseComponent: 尝试挂载已销毁的组件');
            return;
        }

        if (!this.initialized) {
            const startTime = performance.now();

            try {
                this.init();
                this.performanceMetrics.initTime = performance.now() - startTime;

                const renderStartTime = performance.now();
                this.render();
                this.performanceMetrics.renderTime = performance.now() - renderStartTime;

                this.bindEvents();
                this.initialized = true;
                this.mounted = true;

                this.performanceMetrics.mountTime = performance.now() - startTime;

                // 触发挂载完成事件
                this.onMounted();

                if (this.config.debugMode) {
                    console.log(`NotionBaseComponent: 组件挂载完成`, this.performanceMetrics);
                }

            } catch (error) {
                this.handleError('组件初始化失败', error);
            }
        }
    }

    /**
     * 组件从DOM断开时调用
     */
    disconnectedCallback() {
        if (!this.destroyed) {
            this.onBeforeUnmount();
            this.cleanup();
            this.mounted = false;

            if (this.config.debugMode) {
                console.log('NotionBaseComponent: 组件已卸载');
            }
        }
    }

    /**
     * 挂载完成回调
     * 子类可以重写此方法
     */
    onMounted() {
        // 子类实现
    }

    /**
     * 卸载前回调
     * 子类可以重写此方法
     */
    onBeforeUnmount() {
        // 子类实现
    }

    /**
     * 销毁组件
     * 彻底清理所有资源
     */
    destroy() {
        if (this.destroyed) {
            return;
        }

        this.onBeforeUnmount();
        this.cleanup();

        // 清理事件总线监听器
        this.cleanupEventBusListeners();

        // 标记为已销毁
        this.destroyed = true;
        this.mounted = false;

        if (this.config.debugMode) {
            console.log('NotionBaseComponent: 组件已销毁');
        }
    }

    /**
     * 初始化组件
     * 子类应该重写此方法
     */
    init() {
        // 子类实现
    }

    /**
     * 渲染组件内容
     * 子类应该重写此方法
     */
    render() {
        // 子类实现
    }

    /**
     * 绑定事件监听器
     * 子类应该重写此方法
     */
    bindEvents() {
        // 子类实现
    }

    /**
     * 清理资源
     */
    cleanup() {
        // 清理DOM事件监听器
        this.eventListeners.forEach((listener, element) => {
            if (element && typeof element.removeEventListener === 'function') {
                element.removeEventListener(listener.event, listener.handler);
            }
        });
        this.eventListeners.clear();

        // 清理事件总线监听器
        this.cleanupEventBusListeners();
    }

    /**
     * 清理事件总线监听器
     */
    cleanupEventBusListeners() {
        if (this.services.eventBus && this.eventBusListeners.size > 0) {
            this.eventBusListeners.forEach((unsubscribe, eventName) => {
                if (typeof unsubscribe === 'function') {
                    unsubscribe();
                }
            });
            this.eventBusListeners.clear();

            if (this.config.debugMode) {
                console.log('NotionBaseComponent: 清理事件总线监听器');
            }
        }
    }

    // ==================== 组件间通信方法 ====================

    /**
     * 广播事件到事件总线
     * 用于组件间通信
     */
    broadcast(event, data = null) {
        if (!this.services.eventBus) {
            console.warn('NotionBaseComponent: 事件总线未初始化，无法广播事件');
            return;
        }

        try {
            this.services.eventBus.emit(event, data);

            if (this.config.debugMode) {
                console.log(`NotionBaseComponent: 广播事件 "${event}"`, data);
            }
        } catch (error) {
            this.handleError('广播事件失败', error);
        }
    }

    /**
     * 订阅事件总线事件
     * 自动管理监听器生命周期
     */
    subscribe(event, handler, context = null) {
        if (!this.services.eventBus) {
            console.warn('NotionBaseComponent: 事件总线未初始化，无法订阅事件');
            return () => {}; // 返回空的取消订阅函数
        }

        if (typeof handler !== 'function') {
            throw new Error('Event handler must be a function');
        }

        try {
            const unsubscribe = this.services.eventBus.on(event, handler, context || this);

            // 存储取消订阅函数，用于组件销毁时清理
            this.eventBusListeners.set(event, unsubscribe);

            if (this.config.debugMode) {
                console.log(`NotionBaseComponent: 订阅事件 "${event}"`);
            }

            return unsubscribe;
        } catch (error) {
            this.handleError('订阅事件失败', error);
            return () => {};
        }
    }

    /**
     * 订阅一次性事件
     */
    subscribeOnce(event, handler, context = null) {
        if (!this.services.eventBus) {
            console.warn('NotionBaseComponent: 事件总线未初始化，无法订阅一次性事件');
            return () => {};
        }

        if (typeof handler !== 'function') {
            throw new Error('Event handler must be a function');
        }

        try {
            const unsubscribe = this.services.eventBus.once(event, handler, context || this);

            if (this.config.debugMode) {
                console.log(`NotionBaseComponent: 订阅一次性事件 "${event}"`);
            }

            return unsubscribe;
        } catch (error) {
            this.handleError('订阅一次性事件失败', error);
            return () => {};
        }
    }

    /**
     * 取消订阅事件
     */
    unsubscribe(event) {
        if (this.eventBusListeners.has(event)) {
            const unsubscribe = this.eventBusListeners.get(event);
            if (typeof unsubscribe === 'function') {
                unsubscribe();
                this.eventBusListeners.delete(event);

                if (this.config.debugMode) {
                    console.log(`NotionBaseComponent: 取消订阅事件 "${event}"`);
                }
            }
        }
    }

    /**
     * 添加事件监听器（自动管理）
     */
    addEventListenerManaged(element, event, handler) {
        if (element && typeof element.addEventListener === 'function') {
            element.addEventListener(event, handler);
            this.eventListeners.set(element, { event, handler });
        }
    }

    /**
     * 更新组件状态
     * 支持批量更新和状态验证
     */
    setState(newState, callback = null) {
        if (this.destroyed) {
            console.warn('NotionBaseComponent: 尝试更新已销毁组件的状态');
            return;
        }

        if (typeof newState !== 'object' || newState === null) {
            throw new Error('State must be an object');
        }

        const oldState = { ...this.state };
        this.previousState = oldState;

        // 合并新状态
        this.state = { ...this.state, ...newState };

        try {
            // 触发状态变化回调
            this.onStateChange(oldState, this.state);

            // 执行回调函数
            if (typeof callback === 'function') {
                callback(this.state, oldState);
            }

            if (this.config.debugMode) {
                console.log('NotionBaseComponent: 状态已更新', {
                    oldState,
                    newState: this.state,
                    changes: newState
                });
            }

        } catch (error) {
            this.handleError('状态更新失败', error);
        }
    }

    /**
     * 状态变化回调
     * 子类可以重写此方法来响应状态变化
     */
    onStateChange(oldState, newState) {
        // 检查是否需要重新渲染
        if (this.config.autoRender && this.shouldUpdate(oldState, newState)) {
            this.render();
        }
    }

    /**
     * 判断是否需要更新组件
     * 子类可以重写此方法来优化性能
     */
    shouldUpdate(oldState, newState) {
        // 简单的浅比较
        return JSON.stringify(oldState) !== JSON.stringify(newState);
    }

    /**
     * 获取前一个状态
     */
    getPreviousState() {
        return { ...this.previousState };
    }

    /**
     * 重置状态到初始值
     */
    resetState(initialState = {}) {
        this.setState(initialState);
    }

    // ==================== 错误处理系统 ====================

    /**
     * 统一错误处理方法
     */
    handleError(message, error = null) {
        const errorInfo = {
            message,
            error,
            component: this.constructor.name,
            timestamp: new Date().toISOString(),
            state: this.state
        };

        console.error('NotionBaseComponent Error:', errorInfo);

        if (this.config.errorBoundary) {
            this.showError(message);
        }

        // 广播错误事件
        this.broadcast('component:error', errorInfo);

        // 触发错误回调
        this.onError(errorInfo);
    }

    /**
     * 错误回调
     * 子类可以重写此方法来自定义错误处理
     */
    onError(errorInfo) {
        // 子类实现
    }

    // ==================== 配置管理 ====================

    /**
     * 更新组件配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };

        if (this.config.debugMode) {
            console.log('NotionBaseComponent: 配置已更新', this.config);
        }
    }

    /**
     * 获取配置值
     */
    getConfig(key = null) {
        return key ? this.config[key] : { ...this.config };
    }

    /**
     * 创建HTML元素的辅助方法
     */
    createElement(tag, attributes = {}, content = '') {
        const element = document.createElement(tag);
        
        // 设置属性
        Object.entries(attributes).forEach(([key, value]) => {
            if (key === 'className') {
                element.className = value;
            } else if (key === 'innerHTML') {
                element.innerHTML = value;
            } else {
                element.setAttribute(key, value);
            }
        });

        // 设置内容
        if (content) {
            element.textContent = content;
        }

        return element;
    }

    /**
     * 查找子元素的辅助方法
     */
    $(selector) {
        return this.querySelector(selector);
    }

    /**
     * 查找所有子元素的辅助方法
     */
    $$(selector) {
        return this.querySelectorAll(selector);
    }

    /**
     * 发送自定义事件
     */
    emit(eventName, detail = {}) {
        const event = new CustomEvent(eventName, {
            detail,
            bubbles: true,
            cancelable: true
        });
        this.dispatchEvent(event);
    }

    /**
     * 显示加载状态
     */
    showLoading(message = '加载中...') {
        const loadingEl = this.createElement('div', {
            className: 'notion-loading',
            innerHTML: `
                <div class="notion-loading-spinner"></div>
                <div class="notion-loading-text">${message}</div>
            `
        });
        
        this.appendChild(loadingEl);
        return loadingEl;
    }

    /**
     * 隐藏加载状态
     */
    hideLoading() {
        const loadingEl = this.$('.notion-loading');
        if (loadingEl) {
            loadingEl.remove();
        }
    }

    /**
     * 显示错误信息
     */
    showError(message, type = 'error') {
        const errorEl = this.createElement('div', {
            className: `notion-message notion-message-${type}`,
            innerHTML: `
                <div class="notion-message-icon">⚠️</div>
                <div class="notion-message-text">${message}</div>
                <button class="notion-message-close" onclick="this.parentElement.remove()">×</button>
            `
        });
        
        this.appendChild(errorEl);
        
        // 自动隐藏
        setTimeout(() => {
            if (errorEl.parentElement) {
                errorEl.remove();
            }
        }, 5000);
        
        return errorEl;
    }

    // ==================== API请求方法 ====================

    /**
     * 使用API服务发送请求
     * 推荐使用此方法而不是直接的request方法
     */
    async apiRequest(action, data = {}, options = {}) {
        if (!this.services.api) {
            throw new Error('API服务未初始化');
        }

        try {
            return await this.services.api.request(action, data, options);
        } catch (error) {
            this.handleError(`API请求失败: ${action}`, error);
            throw error;
        }
    }

    /**
     * AJAX请求辅助方法（保持向后兼容）
     * 建议使用apiRequest方法
     */
    async request(url, options = {}) {
        const defaultOptions = {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            credentials: 'same-origin'
        };

        const finalOptions = { ...defaultOptions, ...options };

        // 确保body是URLSearchParams格式
        if (finalOptions.method === 'POST') {
            if (typeof finalOptions.body === 'string') {
                finalOptions.body = new URLSearchParams(finalOptions.body);
            } else if (!(finalOptions.body instanceof URLSearchParams)) {
                finalOptions.body = new URLSearchParams(finalOptions.body || '');
            }

            // 转换为字符串
            finalOptions.body = finalOptions.body.toString();
        }

        try {
            const response = await fetch(url, finalOptions);
            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || '请求失败');
            }

            // 检查WordPress AJAX响应格式
            if (data.success === false) {
                throw new Error(data.data?.message || data.data || '请求失败');
            }

            return data;
        } catch (error) {
            this.handleError('请求失败', error);
            throw error;
        }
    }

    // ==================== 性能监控方法 ====================

    /**
     * 获取性能指标
     */
    getPerformanceMetrics() {
        return { ...this.performanceMetrics };
    }

    /**
     * 重置性能指标
     */
    resetPerformanceMetrics() {
        this.performanceMetrics = {
            initTime: 0,
            renderTime: 0,
            mountTime: 0
        };
    }

    /**
     * 检查组件健康状态
     */
    getHealthStatus() {
        return {
            initialized: this.initialized,
            mounted: this.mounted,
            destroyed: this.destroyed,
            servicesAvailable: {
                api: !!this.services.api,
                eventBus: !!this.services.eventBus
            },
            eventListeners: this.eventListeners.size,
            eventBusListeners: this.eventBusListeners.size,
            performance: this.performanceMetrics
        };
    }

    /**
     * 格式化时间的辅助方法
     */
    formatTime(timestamp) {
        if (!timestamp) return '未知';
        
        const date = new Date(timestamp);
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return `${Math.floor(diff / 60000)}分钟前`;
        } else if (diff < 86400000) { // 1天内
            return `${Math.floor(diff / 3600000)}小时前`;
        } else {
            return date.toLocaleDateString('zh-CN');
        }
    }

    /**
     * 格式化文件大小的辅助方法
     */
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
}

// 导出基础组件类
window.NotionBaseComponent = NotionBaseComponent;
