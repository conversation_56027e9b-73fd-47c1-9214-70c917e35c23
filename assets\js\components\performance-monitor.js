/**
 * Notion to WordPress - 性能监控组件
 * 完善的性能监控界面组件，支持异步处理状态、队列管理、操作控制和数据库索引优化
 */

class NotionPerformanceMonitor extends NotionBaseComponent {
    constructor() {
        super();
        this.refreshInterval = null;
        this.state = {
            // 异步处理状态
            asyncStatus: {
                status: 'idle',
                operation: '',
                started_at: '',
                updated_at: '',
                data_count: 0,
                progress: 0,
                details: []
            },
            // 队列管理状态
            queueStatus: {
                total_tasks: 0,
                pending: 0,
                processing: 0,
                completed: 0,
                failed: 0,
                retrying: 0,
                queue_size: 0,
                is_processing: false,
                last_processed: '',
                next_scheduled: ''
            },
            // 数据库索引状态
            indexStatus: {
                total_indexes: 0,
                created_indexes: 0,
                missing_indexes: 0,
                optimization_level: 0,
                last_optimized: ''
            },
            // 系统信息
            systemInfo: {
                php_version: '',
                memory_limit: '',
                current_memory: '',
                peak_memory: '',
                execution_time: 0
            },
            // 性能配置
            performanceConfig: {
                api_page_size: 100,
                concurrent_requests: 5,
                batch_size: 20,
                log_buffer_size: 50,
                enable_performance_mode: true
            },
            loading: false,
            error: null,
            lastRefresh: null
        };

        // 缓存DOM元素引用
        this.elements = {};
    }

    init() {
        // 缓存重要的DOM元素
        this.cacheElements();

        // 从现有HTML读取初始数据
        this.loadExistingData();

        // 绑定事件监听器
        this.bindEvents();

        // 渲染组件增强
        this.render();

        // 加载初始数据
        this.loadInitialData();

        // 启动自动刷新
        this.startAutoRefresh();

        if (this.config.debugMode) {
            console.log('NotionPerformanceMonitor: 组件初始化完成', this.state);
        }
    }

    /**
     * 缓存DOM元素引用
     */
    cacheElements() {
        this.elements = {
            // 性能监控标签页容器
            performanceTab: document.getElementById('performance'),

            // 异步状态容器
            asyncStatusContainer: document.getElementById('async-status-container'),

            // 操作按钮
            refreshButton: document.getElementById('refresh-performance-stats'),
            resetButton: document.getElementById('reset-performance-stats'),

            // 性能卡片容器
            performanceCards: document.querySelector('.notion-wp-performance-cards'),

            // 性能仪表板
            performanceDashboard: document.querySelector('.notion-wp-performance-dashboard')
        };
    }

    /**
     * 从现有HTML读取数据
     */
    loadExistingData() {
        try {
            // 从调试信息中提取初始数据（如果存在）
            const debugInfo = this.elements.performanceTab?.querySelector('[style*="background: #fff3cd"]');
            if (debugInfo) {
                const debugText = debugInfo.textContent;

                // 解析异步状态数据
                const asyncMatch = debugText.match(/异步状态数据:\s*({.*?})/);
                if (asyncMatch) {
                    try {
                        const asyncData = JSON.parse(asyncMatch[1]);
                        this.setState({ asyncStatus: { ...this.state.asyncStatus, ...asyncData } });
                    } catch (e) {
                        console.warn('解析异步状态数据失败:', e);
                    }
                }

                // 解析队列状态数据
                const queueMatch = debugText.match(/队列状态数据:\s*({.*?})/);
                if (queueMatch) {
                    try {
                        const queueData = JSON.parse(queueMatch[1]);
                        this.setState({ queueStatus: { ...this.state.queueStatus, ...queueData } });
                    } catch (e) {
                        console.warn('解析队列状态数据失败:', e);
                    }
                }
            }

            if (this.config.debugMode) {
                console.log('NotionPerformanceMonitor: 已加载现有数据', {
                    asyncStatus: this.state.asyncStatus,
                    queueStatus: this.state.queueStatus
                });
            }
        } catch (error) {
            this.handleError('加载性能监控数据失败', error);
        }
    }

    render() {
        this.innerHTML = `
            <div class="performance-header">
                <h2>📊 性能监控</h2>
                <p>查看插件的性能统计和优化效果。这些数据可以帮助您了解同步速度和资源使用情况。</p>
                <div class="performance-actions">
                    <button class="btn btn-primary" id="refresh-status">
                        <span class="dashicons dashicons-update"></span>
                        刷新状态
                    </button>
                </div>
            </div>

            <div class="performance-dashboard">
                ${this.renderStatusCards()}
                ${this.renderAsyncStatus()}
                ${this.renderQueueStatus()}
            </div>

            ${this.state.loading ? this.renderLoading() : ''}
            ${this.state.error ? this.renderError() : ''}
        `;
    }

    renderStatusCards() {
        const { asyncStatus, queueStatus } = this.state;
        
        return `
            <div class="status-cards">
                <div class="status-card ${asyncStatus.status}">
                    <div class="card-icon">⚡</div>
                    <div class="card-content">
                        <h3>异步处理</h3>
                        <div class="status-value">${this.getStatusText(asyncStatus.status)}</div>
                        <div class="status-detail">${asyncStatus.operation || '空闲中'}</div>
                    </div>
                </div>

                <div class="status-card">
                    <div class="card-icon">📋</div>
                    <div class="card-content">
                        <h3>队列任务</h3>
                        <div class="status-value">${queueStatus.total_tasks}</div>
                        <div class="status-detail">总任务数</div>
                    </div>
                </div>

                <div class="status-card ${queueStatus.processing > 0 ? 'processing' : ''}">
                    <div class="card-icon">🔄</div>
                    <div class="card-content">
                        <h3>处理中</h3>
                        <div class="status-value">${queueStatus.processing}</div>
                        <div class="status-detail">正在处理的任务</div>
                    </div>
                </div>

                <div class="status-card ${queueStatus.failed > 0 ? 'error' : 'success'}">
                    <div class="card-icon">${queueStatus.failed > 0 ? '❌' : '✅'}</div>
                    <div class="card-content">
                        <h3>任务状态</h3>
                        <div class="status-value">${queueStatus.completed}/${queueStatus.total_tasks}</div>
                        <div class="status-detail">${queueStatus.failed > 0 ? `${queueStatus.failed} 个失败` : '全部成功'}</div>
                    </div>
                </div>
            </div>
        `;
    }

    renderAsyncStatus() {
        const { asyncStatus } = this.state;
        
        return `
            <div class="performance-section">
                <h3>异步处理状态</h3>
                <div class="async-status-container">
                    <div class="status-info">
                        <div class="info-row">
                            <span class="label">当前状态:</span>
                            <span class="value status-${asyncStatus.status}">${this.getStatusText(asyncStatus.status)}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">当前操作:</span>
                            <span class="value">${asyncStatus.operation || '无'}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">开始时间:</span>
                            <span class="value">${this.formatTime(asyncStatus.started_at)}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">更新时间:</span>
                            <span class="value">${this.formatTime(asyncStatus.updated_at)}</span>
                        </div>
                        <div class="info-row">
                            <span class="label">数据量:</span>
                            <span class="value">${asyncStatus.data_count} 条</span>
                        </div>
                    </div>
                    
                    ${asyncStatus.progress > 0 ? `
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" style="width: ${asyncStatus.progress}%"></div>
                            </div>
                            <div class="progress-text">${asyncStatus.progress}%</div>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    }

    renderQueueStatus() {
        const { queueStatus } = this.state;
        
        return `
            <div class="performance-section">
                <h3>队列管理状态</h3>
                <div class="queue-status-container">
                    <div class="queue-stats">
                        <div class="stat-item">
                            <div class="stat-value">${queueStatus.pending}</div>
                            <div class="stat-label">等待中</div>
                        </div>
                        <div class="stat-item processing">
                            <div class="stat-value">${queueStatus.processing}</div>
                            <div class="stat-label">处理中</div>
                        </div>
                        <div class="stat-item success">
                            <div class="stat-value">${queueStatus.completed}</div>
                            <div class="stat-label">已完成</div>
                        </div>
                        <div class="stat-item error">
                            <div class="stat-value">${queueStatus.failed}</div>
                            <div class="stat-label">失败</div>
                        </div>
                        <div class="stat-item warning">
                            <div class="stat-value">${queueStatus.retrying}</div>
                            <div class="stat-label">重试中</div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    renderLoading() {
        return `
            <div class="performance-loading">
                <div class="loading-spinner"></div>
                <div class="loading-text">正在加载性能数据...</div>
            </div>
        `;
    }

    renderError() {
        return `
            <div class="performance-error">
                <div class="error-icon">⚠️</div>
                <div class="error-message">${this.state.error}</div>
                <button class="btn btn-secondary" onclick="this.parentElement.style.display='none'">关闭</button>
            </div>
        `;
    }

    bindEvents() {
        // 刷新按钮
        const refreshBtn = this.$('#refresh-status') || this.elements.refreshButton;
        if (refreshBtn) {
            this.addEventListenerManaged(refreshBtn, 'click', () => {
                this.refreshData();
            });
        }

        // 重置按钮
        const resetBtn = this.elements.resetButton;
        if (resetBtn) {
            this.addEventListenerManaged(resetBtn, 'click', () => {
                this.resetPerformanceStats();
            });
        }

        // 异步处理控制按钮
        this.bindAsyncControlButtons();

        // 队列管理按钮
        this.bindQueueControlButtons();

        // 数据库优化按钮
        this.bindDatabaseOptimizationButtons();

        // 标签页切换监听
        this.bindTabSwitchListener();
    }

    /**
     * 绑定异步处理控制按钮
     */
    bindAsyncControlButtons() {
        // 暂停按钮
        const pauseBtn = this.$('#pause-async-processing');
        if (pauseBtn) {
            this.addEventListenerManaged(pauseBtn, 'click', () => {
                this.pauseAsyncProcessing();
            });
        }

        // 恢复按钮
        const resumeBtn = this.$('#resume-async-processing');
        if (resumeBtn) {
            this.addEventListenerManaged(resumeBtn, 'click', () => {
                this.resumeAsyncProcessing();
            });
        }
    }

    /**
     * 绑定队列管理按钮
     */
    bindQueueControlButtons() {
        // 清空队列按钮
        const clearQueueBtn = this.$('#clear-queue');
        if (clearQueueBtn) {
            this.addEventListenerManaged(clearQueueBtn, 'click', () => {
                this.clearQueue();
            });
        }

        // 重试失败任务按钮
        const retryBtn = this.$('#retry-failed-tasks');
        if (retryBtn) {
            this.addEventListenerManaged(retryBtn, 'click', () => {
                this.retryFailedTasks();
            });
        }
    }

    /**
     * 绑定数据库优化按钮
     */
    bindDatabaseOptimizationButtons() {
        // 创建索引按钮
        const createIndexBtn = this.$('#create-database-indexes');
        if (createIndexBtn) {
            this.addEventListenerManaged(createIndexBtn, 'click', () => {
                this.createDatabaseIndexes();
            });
        }

        // 优化表按钮
        const optimizeBtn = this.$('#optimize-database-tables');
        if (optimizeBtn) {
            this.addEventListenerManaged(optimizeBtn, 'click', () => {
                this.optimizeDatabaseTables();
            });
        }
    }

    /**
     * 绑定标签页切换监听
     */
    bindTabSwitchListener() {
        // 监听标签页切换事件
        this.subscribe('tab:switched', (data) => {
            if (data.tabId === 'performance') {
                // 当切换到性能监控标签页时，刷新数据
                this.refreshData();
            }
        });

        // 监听窗口可见性变化
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isVisible()) {
                this.refreshData();
            }
        });
    }

    /**
     * 检查组件是否可见
     */
    isVisible() {
        const performanceTab = this.elements.performanceTab;
        if (!performanceTab) return false;

        // 检查标签页是否激活
        return performanceTab.classList.contains('active') ||
               performanceTab.style.display !== 'none';
    }

    async loadInitialData() {
        await this.refreshData();
    }

    async refreshData() {
        this.setState({ loading: true, error: null });

        try {
            // 并行获取所有性能数据
            const [asyncResponse, queueResponse, indexResponse, systemResponse, configResponse] = await Promise.all([
                this.getAsyncStatus(),
                this.getQueueStatus(),
                this.getIndexStatus(),
                this.getSystemInfo(),
                this.getPerformanceConfig()
            ]);

            this.setState({
                asyncStatus: asyncResponse.data || this.state.asyncStatus,
                queueStatus: queueResponse.data || this.state.queueStatus,
                indexStatus: indexResponse.data || this.state.indexStatus,
                systemInfo: systemResponse.data || this.state.systemInfo,
                performanceConfig: configResponse.data || this.state.performanceConfig,
                loading: false,
                lastRefresh: new Date().toLocaleString()
            });

            // 更新DOM显示
            this.updatePerformanceDisplay();

        } catch (error) {
            console.error('Failed to refresh performance data:', error);
            this.setState({
                loading: false,
                error: '获取性能数据失败: ' + error.message
            });
        }
    }

    /**
     * 获取数据库索引状态
     */
    async getIndexStatus() {
        if (this.services.api) {
            return await this.services.api.request('notion_to_wordpress_get_index_status');
        }

        // 备用AJAX调用
        return await this.request(window.notionToWp.ajax_url, {
            body: new URLSearchParams({
                action: 'notion_to_wordpress_get_index_status',
                nonce: window.notionToWp.nonce
            })
        });
    }

    /**
     * 获取系统信息
     */
    async getSystemInfo() {
        if (this.services.api) {
            return await this.services.api.request('notion_to_wordpress_get_system_info');
        }

        // 备用AJAX调用
        return await this.request(window.notionToWp.ajax_url, {
            body: new URLSearchParams({
                action: 'notion_to_wordpress_get_system_info',
                nonce: window.notionToWp.nonce
            })
        });
    }

    /**
     * 获取性能配置
     */
    async getPerformanceConfig() {
        if (this.services.api) {
            return await this.services.api.request('notion_to_wordpress_get_performance_config');
        }

        // 备用AJAX调用
        return await this.request(window.notionToWp.ajax_url, {
            body: new URLSearchParams({
                action: 'notion_to_wordpress_get_performance_config',
                nonce: window.notionToWp.nonce
            })
        });
    }

    async getAsyncStatus() {
        if (this.services.api) {
            return await this.services.api.request('notion_to_wordpress_get_async_status');
        }

        // 备用AJAX调用
        if (!window.notionToWp) {
            throw new Error('AJAX配置未找到');
        }

        return await this.request(window.notionToWp.ajax_url, {
            body: new URLSearchParams({
                action: 'notion_to_wordpress_get_async_status',
                nonce: window.notionToWp.nonce
            })
        });
    }

    async getQueueStatus() {
        if (this.services.api) {
            return await this.services.api.request('notion_to_wordpress_get_queue_status');
        }

        // 备用AJAX调用
        if (!window.notionToWp) {
            throw new Error('AJAX配置未找到');
        }

        return await this.request(window.notionToWp.ajax_url, {
            body: new URLSearchParams({
                action: 'notion_to_wordpress_get_queue_status',
                nonce: window.notionToWp.nonce
            })
        });
    }

    getStatusText(status) {
        const statusMap = {
            'idle': '空闲',
            'running': '运行中',
            'paused': '已暂停',
            'error': '错误',
            'completed': '已完成'
        };
        return statusMap[status] || status;
    }

    /**
     * 操作控制方法
     */

    /**
     * 暂停异步处理
     */
    async pauseAsyncProcessing() {
        try {
            this.setState({ loading: true });

            const response = this.services.api
                ? await this.services.api.request('notion_to_wordpress_pause_async')
                : await this.request(window.notionToWp.ajax_url, {
                    body: new URLSearchParams({
                        action: 'notion_to_wordpress_pause_async',
                        nonce: window.notionToWp.nonce
                    })
                });

            if (response.success) {
                this.showError('异步处理已暂停', 'success');
                await this.refreshData();
            } else {
                throw new Error(response.data?.message || '暂停失败');
            }
        } catch (error) {
            this.handleError('暂停异步处理失败', error);
        } finally {
            this.setState({ loading: false });
        }
    }

    /**
     * 恢复异步处理
     */
    async resumeAsyncProcessing() {
        try {
            this.setState({ loading: true });

            const response = this.services.api
                ? await this.services.api.request('notion_to_wordpress_resume_async')
                : await this.request(window.notionToWp.ajax_url, {
                    body: new URLSearchParams({
                        action: 'notion_to_wordpress_resume_async',
                        nonce: window.notionToWp.nonce
                    })
                });

            if (response.success) {
                this.showError('异步处理已恢复', 'success');
                await this.refreshData();
            } else {
                throw new Error(response.data?.message || '恢复失败');
            }
        } catch (error) {
            this.handleError('恢复异步处理失败', error);
        } finally {
            this.setState({ loading: false });
        }
    }

    /**
     * 清空队列
     */
    async clearQueue() {
        if (!confirm('确定要清空所有队列任务吗？此操作不可撤销。')) {
            return;
        }

        try {
            this.setState({ loading: true });

            const response = this.services.api
                ? await this.services.api.request('notion_to_wordpress_clear_queue')
                : await this.request(window.notionToWp.ajax_url, {
                    body: new URLSearchParams({
                        action: 'notion_to_wordpress_clear_queue',
                        nonce: window.notionToWp.nonce
                    })
                });

            if (response.success) {
                this.showError('队列已清空', 'success');
                await this.refreshData();
            } else {
                throw new Error(response.data?.message || '清空队列失败');
            }
        } catch (error) {
            this.handleError('清空队列失败', error);
        } finally {
            this.setState({ loading: false });
        }
    }

    /**
     * 重试失败任务
     */
    async retryFailedTasks() {
        try {
            this.setState({ loading: true });

            const response = this.services.api
                ? await this.services.api.request('notion_to_wordpress_retry_failed')
                : await this.request(window.notionToWp.ajax_url, {
                    body: new URLSearchParams({
                        action: 'notion_to_wordpress_retry_failed',
                        nonce: window.notionToWp.nonce
                    })
                });

            if (response.success) {
                this.showError(`已重试 ${response.data.count || 0} 个失败任务`, 'success');
                await this.refreshData();
            } else {
                throw new Error(response.data?.message || '重试失败');
            }
        } catch (error) {
            this.handleError('重试失败任务失败', error);
        } finally {
            this.setState({ loading: false });
        }
    }

    /**
     * 数据库索引优化功能
     */

    /**
     * 创建数据库索引
     */
    async createDatabaseIndexes() {
        try {
            this.setState({ loading: true });

            const response = this.services.api
                ? await this.services.api.request('notion_to_wordpress_create_indexes')
                : await this.request(window.notionToWp.ajax_url, {
                    body: new URLSearchParams({
                        action: 'notion_to_wordpress_create_indexes',
                        nonce: window.notionToWp.nonce
                    })
                });

            if (response.success) {
                const { created, total } = response.data;
                this.showError(`成功创建 ${created}/${total} 个数据库索引`, 'success');
                await this.refreshData();
            } else {
                throw new Error(response.data?.message || '创建索引失败');
            }
        } catch (error) {
            this.handleError('创建数据库索引失败', error);
        } finally {
            this.setState({ loading: false });
        }
    }

    /**
     * 优化数据库表
     */
    async optimizeDatabaseTables() {
        if (!confirm('数据库优化可能需要较长时间，确定要继续吗？')) {
            return;
        }

        try {
            this.setState({ loading: true });

            const response = this.services.api
                ? await this.services.api.request('notion_to_wordpress_optimize_tables')
                : await this.request(window.notionToWp.ajax_url, {
                    body: new URLSearchParams({
                        action: 'notion_to_wordpress_optimize_tables',
                        nonce: window.notionToWp.nonce
                    })
                });

            if (response.success) {
                this.showError('数据库表优化完成', 'success');
                await this.refreshData();
            } else {
                throw new Error(response.data?.message || '优化失败');
            }
        } catch (error) {
            this.handleError('数据库表优化失败', error);
        } finally {
            this.setState({ loading: false });
        }
    }

    /**
     * 重置性能统计
     */
    async resetPerformanceStats() {
        if (!confirm('确定要重置所有性能统计数据吗？此操作不可撤销。')) {
            return;
        }

        try {
            this.setState({ loading: true });

            const response = this.services.api
                ? await this.services.api.request('notion_to_wordpress_reset_stats')
                : await this.request(window.notionToWp.ajax_url, {
                    body: new URLSearchParams({
                        action: 'notion_to_wordpress_reset_stats',
                        nonce: window.notionToWp.nonce
                    })
                });

            if (response.success) {
                this.showError('性能统计已重置', 'success');
                await this.refreshData();
            } else {
                throw new Error(response.data?.message || '重置失败');
            }
        } catch (error) {
            this.handleError('重置性能统计失败', error);
        } finally {
            this.setState({ loading: false });
        }
    }

    /**
     * 更新性能显示
     */
    updatePerformanceDisplay() {
        // 更新异步状态显示
        this.updateAsyncStatusDisplay();

        // 更新队列状态显示
        this.updateQueueStatusDisplay();

        // 更新索引状态显示
        this.updateIndexStatusDisplay();

        // 更新系统信息显示
        this.updateSystemInfoDisplay();

        // 广播更新事件
        this.broadcast('performance:updated', {
            asyncStatus: this.state.asyncStatus,
            queueStatus: this.state.queueStatus,
            indexStatus: this.state.indexStatus,
            systemInfo: this.state.systemInfo,
            lastRefresh: this.state.lastRefresh
        });
    }

    /**
     * 更新异步状态显示
     */
    updateAsyncStatusDisplay() {
        const container = this.elements.asyncStatusContainer;
        if (!container) return;

        const { asyncStatus } = this.state;
        const statusClass = `status-${asyncStatus.status}`;

        // 更新状态指示器
        const statusDisplay = container.querySelector('.async-status-display');
        if (statusDisplay) {
            statusDisplay.className = `async-status-display ${statusClass}`;

            // 更新状态文本
            const statusValue = statusDisplay.querySelector('.status-value');
            if (statusValue) {
                statusValue.textContent = this.getStatusText(asyncStatus.status);
            }

            // 更新详细信息
            this.updateAsyncStatusDetails(statusDisplay, asyncStatus);
        }
    }

    /**
     * 更新异步状态详细信息
     */
    updateAsyncStatusDetails(container, asyncStatus) {
        let detailsContainer = container.querySelector('.status-details');

        if (asyncStatus.operation || asyncStatus.data_count > 0 || asyncStatus.progress > 0) {
            if (!detailsContainer) {
                detailsContainer = document.createElement('div');
                detailsContainer.className = 'status-details';
                container.appendChild(detailsContainer);
            }

            let detailsHTML = '';

            if (asyncStatus.operation) {
                detailsHTML += `
                    <div class="detail-item">
                        <span class="detail-label">当前操作:</span>
                        <span class="detail-value">${this.escapeHtml(asyncStatus.operation)}</span>
                    </div>
                `;
            }

            if (asyncStatus.data_count > 0) {
                detailsHTML += `
                    <div class="detail-item">
                        <span class="detail-label">数据量:</span>
                        <span class="detail-value">${asyncStatus.data_count}</span>
                    </div>
                `;
            }

            if (asyncStatus.progress > 0) {
                detailsHTML += `
                    <div class="detail-item">
                        <span class="detail-label">进度:</span>
                        <span class="detail-value">${asyncStatus.progress}%</span>
                    </div>
                `;
            }

            if (asyncStatus.updated_at) {
                detailsHTML += `
                    <div class="detail-item">
                        <span class="detail-label">更新时间:</span>
                        <span class="detail-value">${this.escapeHtml(asyncStatus.updated_at)}</span>
                    </div>
                `;
            }

            detailsContainer.innerHTML = detailsHTML;
        } else if (detailsContainer) {
            detailsContainer.remove();
        }
    }

    /**
     * 更新队列状态显示
     */
    updateQueueStatusDisplay() {
        // 更新队列统计卡片
        const queueCards = this.querySelectorAll('.status-card');
        const { queueStatus } = this.state;

        queueCards.forEach(card => {
            const cardContent = card.querySelector('.card-content h3');
            if (!cardContent) return;

            const title = cardContent.textContent.trim();
            const statusValue = card.querySelector('.status-value');
            const statusDetail = card.querySelector('.status-detail');

            switch (title) {
                case '队列任务':
                    if (statusValue) statusValue.textContent = queueStatus.total_tasks;
                    if (statusDetail) statusDetail.textContent = '总任务数';
                    break;

                case '处理中':
                    if (statusValue) statusValue.textContent = queueStatus.processing;
                    if (statusDetail) statusDetail.textContent = '正在处理的任务';
                    // 更新状态样式
                    card.className = card.className.replace(/\s*(processing|idle)/g, '');
                    if (queueStatus.processing > 0) {
                        card.classList.add('processing');
                    }
                    break;

                case '任务状态':
                    if (statusValue) statusValue.textContent = `${queueStatus.completed}/${queueStatus.total_tasks}`;
                    if (statusDetail) {
                        statusDetail.textContent = queueStatus.failed > 0
                            ? `${queueStatus.failed} 个失败`
                            : '全部成功';
                    }
                    // 更新状态样式
                    card.className = card.className.replace(/\s*(error|success)/g, '');
                    card.classList.add(queueStatus.failed > 0 ? 'error' : 'success');

                    // 更新图标
                    const cardIcon = card.querySelector('.card-icon');
                    if (cardIcon) {
                        cardIcon.textContent = queueStatus.failed > 0 ? '❌' : '✅';
                    }
                    break;
            }
        });

        // 更新队列统计详情
        const queueStats = this.querySelector('.queue-stats');
        if (queueStats) {
            const statItems = queueStats.querySelectorAll('.stat-item');
            statItems.forEach(item => {
                const label = item.querySelector('.stat-label')?.textContent.trim();
                const value = item.querySelector('.stat-value');

                if (!value) return;

                switch (label) {
                    case '等待中':
                        value.textContent = queueStatus.pending;
                        break;
                    case '处理中':
                        value.textContent = queueStatus.processing;
                        break;
                    case '已完成':
                        value.textContent = queueStatus.completed;
                        break;
                    case '失败':
                        value.textContent = queueStatus.failed;
                        break;
                    case '重试中':
                        value.textContent = queueStatus.retrying;
                        break;
                }
            });
        }
    }

    /**
     * 更新索引状态显示
     */
    updateIndexStatusDisplay() {
        const { indexStatus } = this.state;

        // 如果有索引状态容器，更新显示
        const indexContainer = this.querySelector('.index-status-container');
        if (indexContainer) {
            const indexInfo = indexContainer.querySelector('.index-info');
            if (indexInfo) {
                indexInfo.innerHTML = `
                    <div class="info-row">
                        <span class="label">总索引数:</span>
                        <span class="value">${indexStatus.total_indexes}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">已创建:</span>
                        <span class="value">${indexStatus.created_indexes}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">缺失:</span>
                        <span class="value">${indexStatus.missing_indexes}</span>
                    </div>
                    <div class="info-row">
                        <span class="label">优化级别:</span>
                        <span class="value">${indexStatus.optimization_level}%</span>
                    </div>
                    ${indexStatus.last_optimized ? `
                        <div class="info-row">
                            <span class="label">上次优化:</span>
                            <span class="value">${this.escapeHtml(indexStatus.last_optimized)}</span>
                        </div>
                    ` : ''}
                `;
            }
        }
    }

    /**
     * 更新系统信息显示
     */
    updateSystemInfoDisplay() {
        const { systemInfo } = this.state;

        // 更新系统信息卡片
        const systemInfoContainer = this.querySelector('.notion-wp-system-info');
        if (systemInfoContainer) {
            const configItems = systemInfoContainer.querySelectorAll('.config-item');

            configItems.forEach(item => {
                const label = item.querySelector('.config-label')?.textContent.trim();
                const value = item.querySelector('.config-value');

                if (!value) return;

                switch (label) {
                    case 'PHP版本:':
                        if (systemInfo.php_version) value.textContent = systemInfo.php_version;
                        break;
                    case '内存限制:':
                        if (systemInfo.memory_limit) value.textContent = systemInfo.memory_limit;
                        break;
                    case '当前内存使用:':
                        if (systemInfo.current_memory) value.textContent = systemInfo.current_memory;
                        break;
                    case '峰值内存使用:':
                        if (systemInfo.peak_memory) value.textContent = systemInfo.peak_memory;
                        break;
                }
            });
        }
    }

    startAutoRefresh() {
        // 清除现有的刷新间隔
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        // 每30秒自动刷新一次，但只在组件可见且未加载时刷新
        this.refreshInterval = setInterval(() => {
            if (!this.state.loading && this.isVisible()) {
                this.refreshData();
            }
        }, 30000);

        if (this.config.debugMode) {
            console.log('NotionPerformanceMonitor: 自动刷新已启动 (30秒间隔)');
        }
    }

    /**
     * 停止自动刷新
     */
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;

            if (this.config.debugMode) {
                console.log('NotionPerformanceMonitor: 自动刷新已停止');
            }
        }
    }

    /**
     * 格式化时间显示
     */
    formatTime(timeString) {
        if (!timeString) return '无';

        try {
            const date = new Date(timeString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (error) {
            return timeString;
        }
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        if (typeof text !== 'string') return text;

        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 显示错误或成功消息
     */
    showError(message, type = 'error') {
        // 更新状态中的错误信息
        if (type === 'error') {
            this.setState({ error: message });
        } else {
            // 显示成功消息
            this.setState({ error: null });

            // 创建临时成功提示
            const successDiv = document.createElement('div');
            successDiv.className = 'performance-success';
            successDiv.innerHTML = `
                <div class="success-icon">✅</div>
                <div class="success-message">${this.escapeHtml(message)}</div>
            `;

            // 插入到组件顶部
            this.insertBefore(successDiv, this.firstChild);

            // 3秒后自动移除
            setTimeout(() => {
                if (successDiv.parentNode) {
                    successDiv.remove();
                }
            }, 3000);
        }

        // 重新渲染错误显示
        this.updateErrorDisplay();
    }

    /**
     * 更新错误显示
     */
    updateErrorDisplay() {
        let errorContainer = this.querySelector('.performance-error');

        if (this.state.error) {
            if (!errorContainer) {
                errorContainer = document.createElement('div');
                errorContainer.className = 'performance-error';
                this.appendChild(errorContainer);
            }

            errorContainer.innerHTML = `
                <div class="error-icon">⚠️</div>
                <div class="error-message">${this.escapeHtml(this.state.error)}</div>
                <button class="btn btn-secondary" onclick="this.parentElement.style.display='none'">关闭</button>
            `;
        } else if (errorContainer) {
            errorContainer.remove();
        }
    }

    /**
     * 处理错误
     */
    handleError(message, error) {
        console.error(message, error);
        this.showError(`${message}: ${error.message || error}`);
    }

    cleanup() {
        // 停止自动刷新
        this.stopAutoRefresh();

        // 清理DOM元素引用
        this.elements = {};

        // 调用父类清理方法
        super.cleanup();

        if (this.config.debugMode) {
            console.log('NotionPerformanceMonitor: 组件已清理');
        }
    }
}

// 注册组件
if (window.NotionRegistry) {
    window.NotionRegistry.register('notion-performance-monitor', NotionPerformanceMonitor);
} else {
    console.error('NotionRegistry not found. Make sure component-registry.js is loaded first.');
}
