/**
 * Notion to WordPress - API设置组件
 * 处理Notion API配置、统计卡片显示、连接测试和同步操作
 */

class NotionApiSettings extends NotionBaseComponent {
    constructor() {
        super();
        this.state = {
            apiKey: '',
            databaseId: '',
            syncSchedule: 'manual',
            webhookEnabled: false,
            stats: {
                importedCount: 0,
                publishedCount: 0,
                lastUpdate: '从未',
                nextRun: '未计划'
            },
            isLoading: false,
            isTestingConnection: false,
            isSyncing: false
        };
        
        // 缓存DOM元素引用
        this.elements = {};
    }

    init() {
        // 缓存重要的DOM元素
        this.cacheElements();

        // 从现有HTML表单读取数据
        this.loadExistingData();

        // 绑定事件监听器
        this.bindEvents();

        // 初始化统计卡片
        this.initStatsCards();

        // 设置定时刷新
        this.setupAutoRefresh();

        // 渲染组件增强
        this.render();

        if (this.config.debugMode) {
            console.log('NotionApiSettings: 组件初始化完成', this.state);
        }
    }

    /**
     * 缓存DOM元素引用
     */
    cacheElements() {
        this.elements = {
            // 表单输入元素
            apiKeyInput: document.getElementById('notion_to_wordpress_api_key'),
            databaseIdInput: document.getElementById('notion_to_wordpress_database_id'),
            syncScheduleSelect: document.getElementById('sync_schedule'),
            webhookEnabledCheckbox: document.getElementById('webhook_enabled'),
            
            // 统计卡片元素
            importedCountEl: document.querySelector('.stat-imported-count'),
            publishedCountEl: document.querySelector('.stat-published-count'),
            lastUpdateEl: document.querySelector('.stat-last-update'),
            nextRunEl: document.querySelector('.stat-next-run'),
            
            // 按钮元素
            testConnectionBtn: document.getElementById('notion-test-connection'),
            manualImportBtn: document.getElementById('notion-manual-import'),
            fullImportBtn: document.getElementById('notion-full-import'),
            showHidePasswordBtn: document.querySelector('.show-hide-password'),
            
            // 其他元素
            webhookSettings: document.getElementById('webhook-settings'),
            verificationToken: document.getElementById('verification_token'),
            webhookUrl: document.getElementById('webhook_url')
        };
    }

    /**
     * 从现有HTML表单读取数据
     */
    loadExistingData() {
        try {
            const newState = {};
            
            if (this.elements.apiKeyInput) {
                newState.apiKey = this.elements.apiKeyInput.value || '';
            }
            
            if (this.elements.databaseIdInput) {
                newState.databaseId = this.elements.databaseIdInput.value || '';
            }
            
            if (this.elements.syncScheduleSelect) {
                newState.syncSchedule = this.elements.syncScheduleSelect.value || 'manual';
            }
            
            if (this.elements.webhookEnabledCheckbox) {
                newState.webhookEnabled = this.elements.webhookEnabledCheckbox.checked;
            }
            
            this.setState(newState);
            
            if (this.config.debugMode) {
                console.log('NotionApiSettings: 已加载现有数据', newState);
            }
        } catch (error) {
            this.handleError('加载现有数据失败', error);
        }
    }

    /**
     * 初始化统计卡片
     */
    async initStatsCards() {
        if (this.state.isLoading) return;
        
        this.setState({ isLoading: true });
        
        try {
            // 显示加载状态
            this.showStatsLoading();
            
            // 获取统计数据
            const response = await this.apiRequest('notion_to_wordpress_get_stats');
            
            if (response.success && response.data) {
                this.setState({ 
                    stats: {
                        importedCount: response.data.imported_count || 0,
                        publishedCount: response.data.published_count || 0,
                        lastUpdate: response.data.last_update || '从未',
                        nextRun: response.data.next_run || '未计划'
                    }
                });
                
                // 更新显示
                this.updateStatsDisplay();
                
                if (this.config.debugMode) {
                    console.log('NotionApiSettings: 统计数据已更新', this.state.stats);
                }
            } else {
                throw new Error(response.data?.message || '获取统计数据失败');
            }
        } catch (error) {
            this.handleError('获取统计数据失败', error);
            this.showStatsError();
        } finally {
            this.setState({ isLoading: false });
        }
    }

    /**
     * 显示统计数据加载状态
     */
    showStatsLoading() {
        const loadingText = '加载中...';
        
        if (this.elements.importedCountEl) {
            this.elements.importedCountEl.textContent = loadingText;
        }
        if (this.elements.publishedCountEl) {
            this.elements.publishedCountEl.textContent = loadingText;
        }
        if (this.elements.lastUpdateEl) {
            this.elements.lastUpdateEl.textContent = loadingText;
        }
        if (this.elements.nextRunEl) {
            this.elements.nextRunEl.textContent = loadingText;
        }
    }

    /**
     * 显示统计数据错误状态
     */
    showStatsError() {
        const errorText = '加载失败';
        
        if (this.elements.importedCountEl) {
            this.elements.importedCountEl.textContent = errorText;
        }
        if (this.elements.publishedCountEl) {
            this.elements.publishedCountEl.textContent = errorText;
        }
        if (this.elements.lastUpdateEl) {
            this.elements.lastUpdateEl.textContent = errorText;
        }
        if (this.elements.nextRunEl) {
            this.elements.nextRunEl.textContent = errorText;
        }
    }

    /**
     * 更新统计数据显示
     */
    updateStatsDisplay() {
        const { stats } = this.state;
        
        if (this.elements.importedCountEl) {
            this.elements.importedCountEl.textContent = stats.importedCount.toString();
        }
        if (this.elements.publishedCountEl) {
            this.elements.publishedCountEl.textContent = stats.publishedCount.toString();
        }
        if (this.elements.lastUpdateEl) {
            this.elements.lastUpdateEl.textContent = stats.lastUpdate;
        }
        if (this.elements.nextRunEl) {
            this.elements.nextRunEl.textContent = stats.nextRun;
        }
    }

    /**
     * 设置自动刷新
     */
    setupAutoRefresh() {
        // 每30秒刷新一次统计数据
        setInterval(() => {
            if (!this.state.isLoading && !this.destroyed) {
                this.initStatsCards();
            }
        }, 30000);
        
        // 监听标签页激活事件，激活时刷新数据
        this.subscribe('tab:api-settings:activated', () => {
            setTimeout(() => {
                this.initStatsCards();
            }, 100);
        });
    }

    /**
     * 测试连接
     */
    async testConnection() {
        if (this.state.isTestingConnection) {
            console.warn('NotionApiSettings: 连接测试正在进行中');
            return;
        }
        
        // 验证输入
        const apiKey = this.elements.apiKeyInput?.value?.trim();
        const databaseId = this.elements.databaseIdInput?.value?.trim();
        
        if (!apiKey || !databaseId) {
            this.showError('请先填写API密钥和数据库ID');
            return;
        }
        
        this.setState({ isTestingConnection: true });
        
        // 更新按钮状态
        if (this.elements.testConnectionBtn) {
            this.elements.testConnectionBtn.disabled = true;
            this.elements.testConnectionBtn.innerHTML = '<span class="dashicons dashicons-update spin"></span> 测试中...';
        }
        
        try {
            const response = await this.apiRequest('notion_to_wordpress_test_connection', {
                api_key: apiKey,
                database_id: databaseId
            });
            
            this.showConnectionResult(response);
            
        } catch (error) {
            this.handleError('连接测试失败', error);
            this.showConnectionResult({
                success: false,
                data: { message: error.message || '连接测试失败' }
            });
        } finally {
            this.setState({ isTestingConnection: false });
            
            // 恢复按钮状态
            if (this.elements.testConnectionBtn) {
                this.elements.testConnectionBtn.disabled = false;
                this.elements.testConnectionBtn.innerHTML = '<span class="dashicons dashicons-admin-network"></span> 测试连接';
            }
        }
    }

    /**
     * 显示连接测试结果
     */
    showConnectionResult(response) {
        const isSuccess = response.success;
        const message = response.data?.message || (isSuccess ? '连接成功！' : '连接失败');
        
        // 显示结果消息
        if (isSuccess) {
            this.showError(message, 'success');
            
            // 连接成功后刷新统计数据
            setTimeout(() => {
                this.initStatsCards();
            }, 1000);
        } else {
            this.showError(message, 'error');
        }
        
        // 广播连接测试结果事件
        this.broadcast('api:connection-test-result', {
            success: isSuccess,
            message,
            timestamp: Date.now()
        });
        
        if (this.config.debugMode) {
            console.log('NotionApiSettings: 连接测试结果', response);
        }
    }

    /**
     * 执行手动同步（智能同步）
     */
    async performManualSync() {
        if (this.state.isSyncing) {
            console.warn('NotionApiSettings: 同步操作正在进行中');
            return;
        }

        this.setState({ isSyncing: true });

        // 更新按钮状态
        if (this.elements.manualImportBtn) {
            this.elements.manualImportBtn.disabled = true;
            this.elements.manualImportBtn.innerHTML = '<span class="dashicons dashicons-update spin"></span> 同步中...';
        }

        try {
            const response = await this.apiRequest('notion_to_wordpress_manual_sync', {
                sync_type: 'smart'
            });

            this.showSyncResult(response, '智能同步');

        } catch (error) {
            this.handleError('智能同步失败', error);
            this.showSyncResult({
                success: false,
                data: { message: error.message || '智能同步失败' }
            }, '智能同步');
        } finally {
            this.setState({ isSyncing: false });

            // 恢复按钮状态
            if (this.elements.manualImportBtn) {
                this.elements.manualImportBtn.disabled = false;
                this.elements.manualImportBtn.innerHTML = '<span class="dashicons dashicons-lightbulb"></span> 智能同步';
            }

            // 刷新统计数据
            setTimeout(() => {
                this.initStatsCards();
            }, 2000);
        }
    }

    /**
     * 执行完全同步
     */
    async performFullSync() {
        if (this.state.isSyncing) {
            console.warn('NotionApiSettings: 同步操作正在进行中');
            return;
        }

        // 确认操作
        if (!confirm('完全同步将重新处理所有页面，可能需要较长时间。确定要继续吗？')) {
            return;
        }

        this.setState({ isSyncing: true });

        // 更新按钮状态
        if (this.elements.fullImportBtn) {
            this.elements.fullImportBtn.disabled = true;
            this.elements.fullImportBtn.innerHTML = '<span class="dashicons dashicons-update spin"></span> 同步中...';
        }

        try {
            const response = await this.apiRequest('notion_to_wordpress_full_sync', {
                sync_type: 'full'
            });

            this.showSyncResult(response, '完全同步');

        } catch (error) {
            this.handleError('完全同步失败', error);
            this.showSyncResult({
                success: false,
                data: { message: error.message || '完全同步失败' }
            }, '完全同步');
        } finally {
            this.setState({ isSyncing: false });

            // 恢复按钮状态
            if (this.elements.fullImportBtn) {
                this.elements.fullImportBtn.disabled = false;
                this.elements.fullImportBtn.innerHTML = '<span class="dashicons dashicons-update"></span> 完全同步';
            }

            // 刷新统计数据
            setTimeout(() => {
                this.initStatsCards();
            }, 2000);
        }
    }

    /**
     * 显示同步结果
     */
    showSyncResult(response, syncType) {
        const isSuccess = response.success;
        const message = response.data?.message || (isSuccess ? `${syncType}完成！` : `${syncType}失败`);

        // 显示结果消息
        if (isSuccess) {
            this.showError(message, 'success');
        } else {
            this.showError(message, 'error');
        }

        // 广播同步结果事件
        this.broadcast('api:sync-result', {
            success: isSuccess,
            message,
            syncType,
            timestamp: Date.now(),
            data: response.data
        });

        if (this.config.debugMode) {
            console.log(`NotionApiSettings: ${syncType}结果`, response);
        }
    }

    /**
     * 切换密码显示/隐藏
     */
    togglePasswordVisibility() {
        if (!this.elements.apiKeyInput || !this.elements.showHidePasswordBtn) {
            return;
        }

        const isPassword = this.elements.apiKeyInput.type === 'password';
        const newType = isPassword ? 'text' : 'password';
        const newIcon = isPassword ? 'dashicons-hidden' : 'dashicons-visibility';
        const newTitle = isPassword ? '隐藏密钥' : '显示密钥';

        this.elements.apiKeyInput.type = newType;

        const iconEl = this.elements.showHidePasswordBtn.querySelector('.dashicons');
        if (iconEl) {
            iconEl.className = `dashicons ${newIcon}`;
        }

        this.elements.showHidePasswordBtn.title = newTitle;
    }

    /**
     * 处理表单输入变化
     */
    handleInputChange(event) {
        const { target } = event;
        const { id, value, type, checked } = target;

        let stateUpdate = {};

        switch (id) {
            case 'notion_to_wordpress_api_key':
                stateUpdate.apiKey = value;
                break;
            case 'notion_to_wordpress_database_id':
                stateUpdate.databaseId = value;
                break;
            case 'sync_schedule':
                stateUpdate.syncSchedule = value;
                break;
            case 'webhook_enabled':
                stateUpdate.webhookEnabled = type === 'checkbox' ? checked : value;
                break;
        }

        if (Object.keys(stateUpdate).length > 0) {
            this.setState(stateUpdate);

            if (this.config.debugMode) {
                console.log('NotionApiSettings: 输入变化', stateUpdate);
            }
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 测试连接按钮
        if (this.elements.testConnectionBtn) {
            this.addEventListenerManaged(this.elements.testConnectionBtn, 'click', (e) => {
                e.preventDefault();
                this.testConnection();
            });
        }

        // 智能同步按钮
        if (this.elements.manualImportBtn) {
            this.addEventListenerManaged(this.elements.manualImportBtn, 'click', (e) => {
                e.preventDefault();
                this.performManualSync();
            });
        }

        // 完全同步按钮
        if (this.elements.fullImportBtn) {
            this.addEventListenerManaged(this.elements.fullImportBtn, 'click', (e) => {
                e.preventDefault();
                this.performFullSync();
            });
        }

        // 显示/隐藏密码按钮
        if (this.elements.showHidePasswordBtn) {
            this.addEventListenerManaged(this.elements.showHidePasswordBtn, 'click', (e) => {
                e.preventDefault();
                this.togglePasswordVisibility();
            });
        }

        // 表单输入变化监听
        const inputElements = [
            this.elements.apiKeyInput,
            this.elements.databaseIdInput,
            this.elements.syncScheduleSelect,
            this.elements.webhookEnabledCheckbox
        ];

        inputElements.forEach(element => {
            if (element) {
                const eventType = element.type === 'checkbox' ? 'change' : 'input';
                this.addEventListenerManaged(element, eventType, (e) => {
                    this.handleInputChange(e);
                });
            }
        });

        // 监听全局刷新事件
        this.subscribe('api:refresh-stats', () => {
            this.initStatsCards();
        });

        // 监听标签页切换事件
        this.subscribe('tab:changed', (data) => {
            if (data.tabId === 'api-settings') {
                // 切换到API设置标签页时刷新数据
                setTimeout(() => {
                    this.initStatsCards();
                }, 100);
            }
        });

        if (this.config.debugMode) {
            console.log('NotionApiSettings: 事件绑定完成');
        }
    }

    /**
     * 渲染组件（增强现有HTML结构）
     */
    render() {
        // 在jQuery兼容模式下，不渲染自己的HTML
        // 而是增强现有的HTML结构
        if (!this.elements.apiKeyInput) {
            console.warn('NotionApiSettings: 未找到现有的API设置表单结构');
            return;
        }

        // 添加组件标识
        const apiSettingsContainer = document.getElementById('api-settings');
        if (apiSettingsContainer) {
            apiSettingsContainer.setAttribute('data-enhanced-by', 'notion-api-settings');
        }

        // 更新统计显示
        this.updateStatsDisplay();

        if (this.config.debugMode) {
            console.log('NotionApiSettings: HTML结构增强完成');
        }
    }

    /**
     * 获取当前配置状态
     */
    getCurrentConfig() {
        return {
            apiKey: this.state.apiKey,
            databaseId: this.state.databaseId,
            syncSchedule: this.state.syncSchedule,
            webhookEnabled: this.state.webhookEnabled,
            hasValidConfig: !!(this.state.apiKey && this.state.databaseId)
        };
    }

    /**
     * 验证配置
     */
    validateConfig() {
        const errors = [];

        if (!this.state.apiKey.trim()) {
            errors.push('API密钥不能为空');
        }

        if (!this.state.databaseId.trim()) {
            errors.push('数据库ID不能为空');
        }

        // 验证数据库ID格式（简单验证）
        if (this.state.databaseId && !/^[a-f0-9]{32}$|^[a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12}$/i.test(this.state.databaseId.replace(/-/g, ''))) {
            errors.push('数据库ID格式不正确');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 刷新统计数据（公共方法）
     */
    refreshStats() {
        return this.initStatsCards();
    }

    /**
     * 获取统计数据（公共方法）
     */
    getStats() {
        return { ...this.state.stats };
    }

    /**
     * 组件销毁时的清理
     */
    onBeforeUnmount() {
        // 清除定时器等资源
        if (this.refreshTimer) {
            clearInterval(this.refreshTimer);
        }

        if (this.config.debugMode) {
            console.log('NotionApiSettings: 组件清理完成');
        }
    }
}

// ==================== 组件注册和导出 ====================

// 注册Web Component
if (!customElements.get('notion-api-settings')) {
    customElements.define('notion-api-settings', NotionApiSettings);
}

// 注册到Notion组件注册表
if (window.NotionRegistry) {
    window.NotionRegistry.register('notion-api-settings', NotionApiSettings);
} else {
    console.warn('NotionApiSettings: NotionRegistry未找到，使用标准Web Components注册');
}

// 导出到全局作用域
window.NotionApiSettings = NotionApiSettings;

// 创建全局实例（单例模式）
if (!window.notionApiSettingsInstance) {
    // 延迟创建实例，确保DOM已加载
    document.addEventListener('DOMContentLoaded', () => {
        // 查找API设置容器
        const apiSettingsContainer = document.getElementById('api-settings');

        if (apiSettingsContainer) {
            // 创建组件实例
            const apiSettingsComponent = document.createElement('notion-api-settings');
            apiSettingsComponent.style.display = 'none'; // 隐藏，因为我们只是用来增强现有结构

            // 插入到容器中
            apiSettingsContainer.appendChild(apiSettingsComponent);

            // 保存全局实例引用
            window.notionApiSettingsInstance = apiSettingsComponent;

            console.log('NotionApiSettings: 全局实例已创建并可用');
        } else {
            console.warn('NotionApiSettings: 未找到API设置容器');
        }
    });
}

// 提供便捷的全局API
window.NotionApiSettingsAPI = {
    /**
     * 获取API设置组件实例
     */
    getInstance() {
        return window.notionApiSettingsInstance;
    },

    /**
     * 刷新统计数据
     */
    refreshStats() {
        const instance = this.getInstance();
        return instance ? instance.refreshStats() : Promise.resolve();
    },

    /**
     * 获取统计数据
     */
    getStats() {
        const instance = this.getInstance();
        return instance ? instance.getStats() : null;
    },

    /**
     * 测试连接
     */
    testConnection() {
        const instance = this.getInstance();
        return instance ? instance.testConnection() : Promise.resolve();
    },

    /**
     * 执行智能同步
     */
    performSmartSync() {
        const instance = this.getInstance();
        return instance ? instance.performManualSync() : Promise.resolve();
    },

    /**
     * 执行完全同步
     */
    performFullSync() {
        const instance = this.getInstance();
        return instance ? instance.performFullSync() : Promise.resolve();
    },

    /**
     * 获取当前配置
     */
    getCurrentConfig() {
        const instance = this.getInstance();
        return instance ? instance.getCurrentConfig() : null;
    },

    /**
     * 验证配置
     */
    validateConfig() {
        const instance = this.getInstance();
        return instance ? instance.validateConfig() : { isValid: false, errors: ['组件未初始化'] };
    }
};

console.log('NotionApiSettings: 组件加载完成，支持现有HTML结构增强');
