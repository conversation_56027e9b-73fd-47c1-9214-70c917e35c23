/**
 * Notion Event Bus - 组件间通信系统
 * 
 * 提供发布-订阅模式的事件系统，支持组件间解耦通信
 * 基于单例模式，确保全局唯一的事件总线实例
 * 
 * @since 2.0.0-beta.1
 * <AUTHOR>
 */

class NotionEventBus {
    constructor() {
        if (NotionEventBus.instance) {
            return NotionEventBus.instance;
        }

        // 事件监听器存储
        this.listeners = new Map();
        
        // 一次性监听器存储
        this.onceListeners = new Map();
        
        // 事件历史记录（用于调试）
        this.eventHistory = [];
        this.maxHistorySize = 100;
        
        // 调试模式
        this.debugMode = false;
        
        NotionEventBus.instance = this;
    }

    /**
     * 获取单例实例
     * 
     * @returns {NotionEventBus} 事件总线实例
     */
    static getInstance() {
        if (!NotionEventBus.instance) {
            NotionEventBus.instance = new NotionEventBus();
        }
        return NotionEventBus.instance;
    }

    /**
     * 订阅事件
     * 
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     * @param {Object} context - 回调函数的上下文（可选）
     * @returns {Function} 取消订阅的函数
     */
    on(event, callback, context = null) {
        if (typeof callback !== 'function') {
            throw new Error('Event callback must be a function');
        }

        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }

        const listener = {
            callback,
            context,
            id: this.generateListenerId()
        };

        this.listeners.get(event).push(listener);

        if (this.debugMode) {
            console.log(`EventBus: 订阅事件 "${event}", 监听器ID: ${listener.id}`);
        }

        // 返回取消订阅函数
        return () => this.off(event, listener.id);
    }

    /**
     * 订阅事件（仅触发一次）
     * 
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     * @param {Object} context - 回调函数的上下文（可选）
     * @returns {Function} 取消订阅的函数
     */
    once(event, callback, context = null) {
        if (typeof callback !== 'function') {
            throw new Error('Event callback must be a function');
        }

        if (!this.onceListeners.has(event)) {
            this.onceListeners.set(event, []);
        }

        const listener = {
            callback,
            context,
            id: this.generateListenerId()
        };

        this.onceListeners.get(event).push(listener);

        if (this.debugMode) {
            console.log(`EventBus: 订阅一次性事件 "${event}", 监听器ID: ${listener.id}`);
        }

        // 返回取消订阅函数
        return () => this.offOnce(event, listener.id);
    }

    /**
     * 取消事件订阅
     * 
     * @param {string} event - 事件名称
     * @param {string|Function} callbackOrId - 回调函数或监听器ID
     */
    off(event, callbackOrId) {
        if (!this.listeners.has(event)) {
            return;
        }

        const listeners = this.listeners.get(event);
        const index = listeners.findIndex(listener => 
            listener.id === callbackOrId || listener.callback === callbackOrId
        );

        if (index !== -1) {
            listeners.splice(index, 1);
            
            if (this.debugMode) {
                console.log(`EventBus: 取消订阅事件 "${event}"`);
            }
        }

        // 如果没有监听器了，删除事件
        if (listeners.length === 0) {
            this.listeners.delete(event);
        }
    }

    /**
     * 取消一次性事件订阅
     * 
     * @param {string} event - 事件名称
     * @param {string|Function} callbackOrId - 回调函数或监听器ID
     */
    offOnce(event, callbackOrId) {
        if (!this.onceListeners.has(event)) {
            return;
        }

        const listeners = this.onceListeners.get(event);
        const index = listeners.findIndex(listener => 
            listener.id === callbackOrId || listener.callback === callbackOrId
        );

        if (index !== -1) {
            listeners.splice(index, 1);
            
            if (this.debugMode) {
                console.log(`EventBus: 取消一次性事件订阅 "${event}"`);
            }
        }

        // 如果没有监听器了，删除事件
        if (listeners.length === 0) {
            this.onceListeners.delete(event);
        }
    }

    /**
     * 发布事件
     * 
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     * @param {Object} options - 发布选项
     */
    emit(event, data = null, options = {}) {
        const timestamp = Date.now();
        
        // 记录事件历史
        this.recordEvent(event, data, timestamp);

        if (this.debugMode) {
            console.log(`EventBus: 发布事件 "${event}"`, data);
        }

        // 触发普通监听器
        if (this.listeners.has(event)) {
            const listeners = [...this.listeners.get(event)]; // 复制数组避免修改问题
            
            listeners.forEach(listener => {
                try {
                    if (listener.context) {
                        listener.callback.call(listener.context, data, event);
                    } else {
                        listener.callback(data, event);
                    }
                } catch (error) {
                    console.error(`EventBus: 事件 "${event}" 监听器执行错误:`, error);
                }
            });
        }

        // 触发一次性监听器
        if (this.onceListeners.has(event)) {
            const onceListeners = [...this.onceListeners.get(event)]; // 复制数组
            
            // 清空一次性监听器
            this.onceListeners.delete(event);
            
            onceListeners.forEach(listener => {
                try {
                    if (listener.context) {
                        listener.callback.call(listener.context, data, event);
                    } else {
                        listener.callback(data, event);
                    }
                } catch (error) {
                    console.error(`EventBus: 一次性事件 "${event}" 监听器执行错误:`, error);
                }
            });
        }

        // 异步模式（如果需要）
        if (options.async) {
            setTimeout(() => {
                // 异步处理逻辑可以在这里添加
            }, 0);
        }
    }

    /**
     * 移除所有事件监听器
     * 
     * @param {string} event - 事件名称（可选，如果不提供则清除所有事件）
     */
    removeAllListeners(event = null) {
        if (event) {
            this.listeners.delete(event);
            this.onceListeners.delete(event);
            
            if (this.debugMode) {
                console.log(`EventBus: 清除事件 "${event}" 的所有监听器`);
            }
        } else {
            this.listeners.clear();
            this.onceListeners.clear();
            
            if (this.debugMode) {
                console.log('EventBus: 清除所有事件监听器');
            }
        }
    }

    /**
     * 获取事件的监听器数量
     * 
     * @param {string} event - 事件名称
     * @returns {number} 监听器数量
     */
    listenerCount(event) {
        const normalCount = this.listeners.has(event) ? this.listeners.get(event).length : 0;
        const onceCount = this.onceListeners.has(event) ? this.onceListeners.get(event).length : 0;
        return normalCount + onceCount;
    }

    /**
     * 获取所有事件名称
     * 
     * @returns {Array<string>} 事件名称数组
     */
    eventNames() {
        const normalEvents = Array.from(this.listeners.keys());
        const onceEvents = Array.from(this.onceListeners.keys());
        return [...new Set([...normalEvents, ...onceEvents])];
    }

    /**
     * 启用/禁用调试模式
     * 
     * @param {boolean} enabled - 是否启用调试模式
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        console.log(`EventBus: 调试模式 ${enabled ? '启用' : '禁用'}`);
    }

    /**
     * 获取事件历史记录
     * 
     * @param {number} limit - 返回的记录数量限制
     * @returns {Array} 事件历史记录
     */
    getEventHistory(limit = 50) {
        return this.eventHistory.slice(-limit);
    }

    /**
     * 清除事件历史记录
     */
    clearEventHistory() {
        this.eventHistory = [];
        
        if (this.debugMode) {
            console.log('EventBus: 清除事件历史记录');
        }
    }

    // ==================== 私有方法 ====================

    /**
     * 生成监听器ID
     * 
     * @returns {string} 唯一的监听器ID
     */
    generateListenerId() {
        return 'listener_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 记录事件到历史记录
     * 
     * @param {string} event - 事件名称
     * @param {*} data - 事件数据
     * @param {number} timestamp - 时间戳
     */
    recordEvent(event, data, timestamp) {
        this.eventHistory.push({
            event,
            data,
            timestamp,
            date: new Date(timestamp).toISOString()
        });

        // 限制历史记录大小
        if (this.eventHistory.length > this.maxHistorySize) {
            this.eventHistory.shift();
        }
    }
}

// 导出为ES6模块（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotionEventBus;
}

// 同时支持全局访问
if (typeof window !== 'undefined') {
    window.NotionEventBus = NotionEventBus;
}
