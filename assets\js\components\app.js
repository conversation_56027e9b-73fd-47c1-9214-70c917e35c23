/**
 * Notion to WordPress - 主应用入口
 * 协调所有组件的生命周期管理和应用初始化
 */

class NotionAdminApp {
    constructor() {
        this.components = new Map();
        this.services = new Map();
        this.initialized = false;
        this.config = {
            debugMode: window.notionToWp?.debug || false,
            version: window.notionToWp?.version || '2.0.0',
            ajaxUrl: window.notionToWp?.ajax_url || '/wp-admin/admin-ajax.php',
            nonce: window.notionToWp?.nonce || ''
        };
        
        // 绑定方法上下文
        this.init = this.init.bind(this);
        this.startApp = this.startApp.bind(this);
        this.handleError = this.handleError.bind(this);
    }

    /**
     * 初始化应用
     */
    async init() {
        if (this.initialized) {
            console.warn('NotionAdminApp: Already initialized');
            return;
        }

        try {
            console.log('NotionAdminApp: Starting initialization...');
            
            // 检查环境依赖
            if (!this.checkEnvironment()) {
                throw new Error('Environment check failed');
            }
            
            // 等待DOM加载完成
            await this.waitForDOM();
            
            // 检查页面环境
            if (!this.isAdminPage()) {
                console.log('NotionAdminApp: Not on admin page, skipping initialization');
                return;
            }
            
            // 初始化服务
            await this.initializeServices();
            
            // 注册所有组件
            await this.registerAllComponents();
            
            // 启动应用
            await this.startApp();
            
            // 设置错误处理
            this.setupErrorHandling();
            
            // 设置性能监控
            this.setupPerformanceMonitoring();
            
            this.initialized = true;
            console.log('NotionAdminApp: Initialization complete');
            
            // 触发初始化完成事件
            this.publishEvent('app:initialized', { 
                version: this.config.version,
                components: Array.from(this.components.keys()),
                services: Array.from(this.services.keys())
            });
            
        } catch (error) {
            this.handleError('Initialization failed', error);
        }
    }

    /**
     * 检查环境依赖
     */
    checkEnvironment() {
        const requirements = [
            { name: 'Custom Elements', check: () => 'customElements' in window },
            { name: 'ES6 Classes', check: () => {
                try {
                    eval('class TestClass {}');
                    return true;
                } catch (e) {
                    return false;
                }
            }},
            { name: 'Fetch API', check: () => 'fetch' in window },
            { name: 'Promise', check: () => 'Promise' in window },
            { name: 'Map/Set', check: () => 'Map' in window && 'Set' in window },
            { name: 'MutationObserver', check: () => 'MutationObserver' in window },
            { name: 'NotionBaseComponent', check: () => typeof window.NotionBaseComponent === 'function' },
            { name: 'NotionRegistry', check: () => window.NotionRegistry && typeof window.NotionRegistry.register === 'function' }
        ];

        const missing = requirements.filter(req => !req.check());
        
        if (missing.length > 0) {
            console.error('NotionAdminApp: Missing requirements:', missing.map(r => r.name));
            return false;
        }

        if (this.config.debugMode) {
            console.log('NotionAdminApp: All environment requirements satisfied');
        }
        
        return true;
    }

    /**
     * 等待DOM加载完成
     */
    waitForDOM() {
        return new Promise((resolve) => {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', resolve);
            } else {
                resolve();
            }
        });
    }

    /**
     * 检查是否在管理页面
     */
    isAdminPage() {
        return document.querySelector('.notion-wp-admin-page') !== null ||
               document.querySelector('#notion-to-wordpress-plugin-admin') !== null ||
               window.location.href.includes('notion-to-wordpress');
    }

    /**
     * 初始化服务
     */
    async initializeServices() {
        try {
            // 初始化API服务
            if (window.NotionApiService) {
                const apiService = new window.NotionApiService();
                this.services.set('api', apiService);
                if (this.config.debugMode) {
                    console.log('NotionAdminApp: API service initialized');
                }
            }
            
            // 初始化事件总线
            if (window.NotionEventBus) {
                const eventBus = window.NotionEventBus.getInstance();
                this.services.set('eventBus', eventBus);
                if (this.config.debugMode) {
                    console.log('NotionAdminApp: Event bus initialized');
                }
            }
            
            // 将服务注入到全局，供组件使用
            window.notionServices = Object.fromEntries(this.services);
            
        } catch (error) {
            console.error('NotionAdminApp: Service initialization failed', error);
            throw error;
        }
    }

    /**
     * 注册所有组件
     */
    async registerAllComponents() {
        const componentDefinitions = [
            { name: 'notion-tab-manager', class: 'NotionTabManager', required: true },
            { name: 'notion-api-settings', class: 'NotionApiSettings', required: true },
            { name: 'notion-field-mapping', class: 'NotionFieldMapping', required: true },
            { name: 'notion-other-settings', class: 'NotionOtherSettings', required: true },
            { name: 'notion-performance-monitor', class: 'NotionPerformanceMonitor', required: true },
            { name: 'notion-debug-tools', class: 'NotionDebugTools', required: true },
            { name: 'notion-help-docs', class: 'NotionHelpDocs', required: true },
            { name: 'notion-about-author', class: 'NotionAboutAuthor', required: true }
        ];

        let registeredCount = 0;
        let failedCount = 0;

        for (const def of componentDefinitions) {
            try {
                // 检查组件类是否存在
                const ComponentClass = window[def.class];
                if (!ComponentClass) {
                    if (def.required) {
                        console.error(`NotionAdminApp: Required component class ${def.class} not found`);
                        failedCount++;
                    } else {
                        console.warn(`NotionAdminApp: Optional component class ${def.class} not found`);
                    }
                    continue;
                }

                // 检查是否已经注册
                if (window.NotionRegistry.has(def.name)) {
                    if (this.config.debugMode) {
                        console.log(`NotionAdminApp: Component ${def.name} already registered`);
                    }
                    registeredCount++;
                    continue;
                }

                // 注册组件
                window.NotionRegistry.register(def.name, ComponentClass);
                this.components.set(def.name, {
                    class: ComponentClass,
                    required: def.required,
                    instances: new Set()
                });
                
                registeredCount++;
                
                if (this.config.debugMode) {
                    console.log(`NotionAdminApp: Registered component ${def.name}`);
                }
                
            } catch (error) {
                console.error(`NotionAdminApp: Failed to register component ${def.name}`, error);
                failedCount++;
            }
        }

        console.log(`NotionAdminApp: Component registration complete - ${registeredCount} registered, ${failedCount} failed`);
        
        if (failedCount > 0 && registeredCount === 0) {
            throw new Error('No components were successfully registered');
        }
    }

    /**
     * 启动应用
     */
    async startApp() {
        try {
            // 扫描并初始化页面中的组件实例
            this.scanAndInitializeComponents();
            
            // 设置组件监听器
            this.setupComponentObserver();
            
            // 初始化标签页管理器（如果存在）
            await this.initializeTabManager();
            
            // 设置应用级事件监听
            this.setupAppEventListeners();
            
            if (this.config.debugMode) {
                console.log('NotionAdminApp: Application started successfully');
            }
            
        } catch (error) {
            console.error('NotionAdminApp: Failed to start application', error);
            throw error;
        }
    }

    /**
     * 扫描并初始化页面中的组件实例
     */
    scanAndInitializeComponents() {
        let totalInstances = 0;
        
        this.components.forEach((componentInfo, name) => {
            const elements = document.querySelectorAll(name);
            elements.forEach(element => {
                componentInfo.instances.add(element);
                totalInstances++;
                
                // 如果组件有特殊的初始化需求，在这里处理
                if (element.notionInit && typeof element.notionInit === 'function') {
                    try {
                        element.notionInit();
                    } catch (error) {
                        console.error(`NotionAdminApp: Failed to initialize ${name}`, error);
                    }
                }
            });
        });
        
        if (this.config.debugMode) {
            console.log(`NotionAdminApp: Found and initialized ${totalInstances} component instances`);
        }
    }

    /**
     * 设置组件观察器
     */
    setupComponentObserver() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                // 处理新增的组件
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.handleAddedNode(node);
                    }
                });

                // 处理移除的组件
                mutation.removedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.handleRemovedNode(node);
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });

        // 保存观察器引用以便清理
        this.componentObserver = observer;
    }

    /**
     * 处理新增的节点
     */
    handleAddedNode(node) {
        const tagName = node.tagName?.toLowerCase();
        if (tagName && this.components.has(tagName)) {
            const componentInfo = this.components.get(tagName);
            componentInfo.instances.add(node);
            
            if (this.config.debugMode) {
                console.log(`NotionAdminApp: Added component instance ${tagName}`);
            }
        }

        // 检查子节点
        if (node.querySelectorAll) {
            this.components.forEach((componentInfo, name) => {
                const childElements = node.querySelectorAll(name);
                childElements.forEach(element => {
                    componentInfo.instances.add(element);
                });
            });
        }
    }

    /**
     * 处理移除的节点
     */
    handleRemovedNode(node) {
        const tagName = node.tagName?.toLowerCase();
        if (tagName && this.components.has(tagName)) {
            const componentInfo = this.components.get(tagName);
            componentInfo.instances.delete(node);

            if (this.config.debugMode) {
                console.log(`NotionAdminApp: Removed component instance ${tagName}`);
            }
        }

        // 检查子节点
        if (node.querySelectorAll) {
            this.components.forEach((componentInfo, name) => {
                const childElements = node.querySelectorAll(name);
                childElements.forEach(element => {
                    componentInfo.instances.delete(element);
                });
            });
        }
    }

    /**
     * 初始化标签页管理器
     */
    async initializeTabManager() {
        const tabManagerElement = document.querySelector('notion-tab-manager');
        if (tabManagerElement) {
            if (this.config.debugMode) {
                console.log('NotionAdminApp: Tab manager found, initializing...');
            }
            return;
        }

        // 如果页面中没有标签页管理器，创建一个
        const container = document.querySelector('.notion-wp-content') ||
                         document.querySelector('.wrap') ||
                         document.querySelector('#wpbody-content');

        if (container && this.components.has('notion-tab-manager')) {
            const tabManager = document.createElement('notion-tab-manager');
            container.appendChild(tabManager);

            if (this.config.debugMode) {
                console.log('NotionAdminApp: Tab manager created and added to page');
            }
        }
    }

    /**
     * 设置应用级事件监听
     */
    setupAppEventListeners() {
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (document.hidden) {
                this.publishEvent('app:hidden');
            } else {
                this.publishEvent('app:visible');
            }
        });

        // 监听窗口大小变化
        let resizeTimeout;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimeout);
            resizeTimeout = setTimeout(() => {
                this.publishEvent('app:resize', {
                    width: window.innerWidth,
                    height: window.innerHeight
                });
            }, 250);
        });

        // 监听页面卸载
        window.addEventListener('beforeunload', () => {
            this.cleanup();
        });
    }

    /**
     * 设置错误处理
     */
    setupErrorHandling() {
        // 全局JavaScript错误处理
        window.addEventListener('error', (event) => {
            if (event.filename && (
                event.filename.includes('components/') ||
                event.filename.includes('notion-to-wordpress')
            )) {
                this.handleError('JavaScript Error', event.error, {
                    filename: event.filename,
                    lineno: event.lineno,
                    colno: event.colno
                });
            }
        });

        // 未处理的Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            if (event.reason && event.reason.stack &&
                event.reason.stack.includes('notion-to-wordpress')) {
                this.handleError('Promise Rejection', event.reason);
                event.preventDefault(); // 防止错误在控制台显示
            }
        });
    }

    /**
     * 设置性能监控
     */
    setupPerformanceMonitoring() {
        if (!this.config.debugMode) return;

        // 监控组件渲染性能
        const observer = new PerformanceObserver((list) => {
            list.getEntries().forEach((entry) => {
                if (entry.name.includes('notion-')) {
                    console.log(`Performance: ${entry.name} took ${entry.duration.toFixed(2)}ms`);
                }
            });
        });

        try {
            observer.observe({ entryTypes: ['measure'] });
        } catch (e) {
            // Performance Observer 可能不被支持
            if (this.config.debugMode) {
                console.log('NotionAdminApp: Performance monitoring not available');
            }
        }
    }

    /**
     * 发布事件
     */
    publishEvent(eventName, data = {}) {
        const eventBus = this.services.get('eventBus');
        if (eventBus && eventBus.publish) {
            eventBus.publish(eventName, data);
        }

        // 同时触发DOM事件
        const customEvent = new CustomEvent(`notion:${eventName}`, {
            detail: data,
            bubbles: true
        });
        document.dispatchEvent(customEvent);
    }

    /**
     * 错误处理
     */
    handleError(message, error, context = {}) {
        const errorInfo = {
            message,
            error: error?.message || error,
            stack: error?.stack,
            context,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            url: window.location.href
        };

        console.error('NotionAdminApp Error:', errorInfo);

        // 发布错误事件
        this.publishEvent('app:error', errorInfo);

        // 如果有错误报告服务，在这里发送错误报告
        if (this.config.debugMode) {
            console.table(errorInfo);
        }
    }

    /**
     * 获取应用状态
     */
    getStatus() {
        const componentStats = {};
        this.components.forEach((info, name) => {
            componentStats[name] = {
                instances: info.instances.size,
                required: info.required
            };
        });

        return {
            initialized: this.initialized,
            version: this.config.version,
            debugMode: this.config.debugMode,
            components: componentStats,
            services: Array.from(this.services.keys()),
            totalComponentInstances: Array.from(this.components.values())
                .reduce((total, info) => total + info.instances.size, 0)
        };
    }

    /**
     * 重新初始化应用
     */
    async reinitialize() {
        console.log('NotionAdminApp: Reinitializing...');

        // 清理现有状态
        this.cleanup();

        // 重置状态
        this.initialized = false;
        this.components.clear();
        this.services.clear();

        // 重新初始化
        await this.init();
    }

    /**
     * 清理资源
     */
    cleanup() {
        if (this.config.debugMode) {
            console.log('NotionAdminApp: Cleaning up resources...');
        }

        // 清理组件观察器
        if (this.componentObserver) {
            this.componentObserver.disconnect();
            this.componentObserver = null;
        }

        // 清理组件实例
        this.components.forEach((info, name) => {
            info.instances.forEach(instance => {
                if (instance.cleanup && typeof instance.cleanup === 'function') {
                    try {
                        instance.cleanup();
                    } catch (error) {
                        console.error(`NotionAdminApp: Error cleaning up ${name}`, error);
                    }
                }
            });
            info.instances.clear();
        });

        // 清理服务
        this.services.forEach((service, name) => {
            if (service.cleanup && typeof service.cleanup === 'function') {
                try {
                    service.cleanup();
                } catch (error) {
                    console.error(`NotionAdminApp: Error cleaning up service ${name}`, error);
                }
            }
        });

        // 发布清理完成事件
        this.publishEvent('app:cleanup');

        this.initialized = false;
    }
}

// 创建全局应用实例
window.NotionAdminApp = new NotionAdminApp();

// 提供调试接口
window.NotionAppDebug = {
    getStatus: () => window.NotionAdminApp.getStatus(),
    reinitialize: () => window.NotionAdminApp.reinitialize(),
    cleanup: () => window.NotionAdminApp.cleanup(),
    publishEvent: (name, data) => window.NotionAdminApp.publishEvent(name, data)
};

// 自动初始化应用
window.NotionAdminApp.init().catch(error => {
    console.error('NotionAdminApp: Failed to initialize', error);
});

// 在控制台显示启动信息
console.log('%c🚀 Notion to WordPress Admin App', 'color: #2563eb; font-weight: bold; font-size: 16px;');
console.log('%cVersion: ' + (window.notionToWp?.version || '2.0.0'), 'color: #6b7280; font-size: 12px;');
console.log('%cUse NotionAppDebug for debugging tools', 'color: #6b7280; font-size: 12px;');
