/**
 * Notion to WordPress - 帮助文档组件
 * 展示快速开始指南、FAQ和详细使用说明，支持搜索功能和内容折叠展开
 */

class NotionHelpDocs extends NotionBaseComponent {
    constructor() {
        super();
        this.state = {
            // 帮助内容数据
            helpSections: [],
            // 搜索状态
            searchTerm: '',
            filteredSections: [],
            // 界面状态
            isLoading: false,
            error: null,
            // 导航状态
            activeSection: 'quick-start',
            expandedSections: new Set(['quick-start'])
        };
        
        // 缓存DOM元素引用
        this.elements = {};
    }

    init() {
        // 缓存DOM元素
        this.cacheElements();
        
        // 加载帮助内容
        this.loadHelpContent();
        
        // 初始化搜索功能
        this.initSearchFunction();
        
        // 绑定事件监听器
        this.bindEvents();
        
        // 渲染组件增强
        this.render();
        
        if (this.config.debugMode) {
            console.log('NotionHelpDocs: 组件初始化完成', this.state);
        }
    }

    /**
     * 缓存DOM元素引用
     */
    cacheElements() {
        this.elements = {
            // 帮助文档标签页容器
            helpTab: document.getElementById('help'),
            
            // 现有的帮助内容区域
            helpSections: document.querySelectorAll('.notion-wp-help-section'),
            
            // 设置区域
            settingsSection: document.querySelector('#help .notion-wp-settings-section')
        };
    }

    /**
     * 加载帮助内容
     */
    loadHelpContent() {
        const helpSections = [
            {
                id: 'quick-start',
                title: '🚀 快速开始',
                icon: '🚀',
                content: this.getQuickStartContent(),
                expanded: true,
                priority: 1
            },
            {
                id: 'faq',
                title: '❓ 常见问题',
                icon: '❓',
                content: this.getFAQContent(),
                expanded: false,
                priority: 2
            },
            {
                id: 'detailed-guide',
                title: '📖 详细说明',
                icon: '📖',
                content: this.getDetailedGuideContent(),
                expanded: false,
                priority: 3
            },
            {
                id: 'troubleshooting',
                title: '🔧 故障排除',
                icon: '🔧',
                content: this.getTroubleshootingContent(),
                expanded: false,
                priority: 4
            },
            {
                id: 'advanced-usage',
                title: '⚡ 高级用法',
                icon: '⚡',
                content: this.getAdvancedUsageContent(),
                expanded: false,
                priority: 5
            },
            {
                id: 'support',
                title: '💬 获取支持',
                icon: '💬',
                content: this.getSupportContent(),
                expanded: false,
                priority: 6
            }
        ];

        this.setState({ 
            helpSections,
            filteredSections: helpSections
        });
        
        if (this.config.debugMode) {
            console.log('NotionHelpDocs: 已加载帮助内容', helpSections);
        }
    }

    /**
     * 获取快速开始内容
     */
    getQuickStartContent() {
        return `
            <div class="help-content-section">
                <p class="section-intro">按照以下步骤快速设置和使用 Notion to WordPress 插件：</p>
                <ol class="help-steps">
                    <li>
                        <strong>创建 Notion 集成</strong>
                        <p>访问 <a href="https://www.notion.so/my-integrations" target="_blank">Notion 集成页面</a>，创建新的集成并获取 API 密钥。</p>
                    </li>
                    <li>
                        <strong>准备 Notion 数据库</strong>
                        <p>在 Notion 中创建一个数据库，并与您的集成共享。确保数据库包含标题、内容等必要字段。</p>
                    </li>
                    <li>
                        <strong>获取数据库 ID</strong>
                        <p>从 Notion 数据库 URL 中复制数据库 ID（32位字符串）。</p>
                    </li>
                    <li>
                        <strong>配置插件设置</strong>
                        <p>在 "API 设置" 标签页中输入 API 密钥和数据库 ID。</p>
                    </li>
                    <li>
                        <strong>设置字段映射</strong>
                        <p>在 "字段映射" 标签页中配置 Notion 属性与 WordPress 字段的对应关系。</p>
                    </li>
                    <li>
                        <strong>测试连接</strong>
                        <p>点击 "测试连接" 按钮确认设置正确。</p>
                    </li>
                    <li>
                        <strong>保存设置</strong>
                        <p>点击 "保存所有设置" 保存您的配置。</p>
                    </li>
                    <li>
                        <strong>开始同步</strong>
                        <p>点击 "手动同步" 或设置自动同步频率开始导入内容。</p>
                    </li>
                </ol>
                <div class="help-tip">
                    <strong>💡 提示：</strong>首次使用建议先进行手动同步，确认一切正常后再启用自动同步。
                </div>
            </div>
        `;
    }

    /**
     * 获取常见问题内容
     */
    getFAQContent() {
        return `
            <div class="help-content-section">
                <div class="faq-item">
                    <h4 class="faq-question">❓ 为什么我的 Notion 页面没有导入？</h4>
                    <div class="faq-answer">
                        <p><strong>可能的原因和解决方案：</strong></p>
                        <ul>
                            <li>确认 API 密钥和数据库 ID 正确</li>
                            <li>确认 Notion 集成已与数据库共享</li>
                            <li>检查字段映射是否正确对应 Notion 中的属性名称</li>
                            <li>尝试使用 "完全同步" 按钮重新同步</li>
                            <li>查看调试工具中的错误日志</li>
                        </ul>
                    </div>
                </div>

                <div class="faq-item">
                    <h4 class="faq-question">❓ 如何自定义导入的内容格式？</h4>
                    <div class="faq-answer">
                        <p>本插件会尽可能保留 Notion 中的格式，包括：</p>
                        <ul>
                            <li>标题层级（H1-H6）</li>
                            <li>列表（有序和无序）</li>
                            <li>表格和代码块</li>
                            <li>图片和媒体文件</li>
                            <li>数学公式和图表</li>
                        </ul>
                        <p>您可以在 "字段映射" 中配置自定义字段的处理方式。</p>
                    </div>
                </div>

                <div class="faq-item">
                    <h4 class="faq-question">❓ 导入后如何更新内容？</h4>
                    <div class="faq-answer">
                        <p>有以下几种更新方式：</p>
                        <ul>
                            <li><strong>手动同步：</strong>点击 "手动同步" 按钮</li>
                            <li><strong>智能同步：</strong>只同步有变化的内容</li>
                            <li><strong>完全同步：</strong>重新同步所有内容</li>
                            <li><strong>自动同步：</strong>设置定时自动同步</li>
                        </ul>
                    </div>
                </div>

                <div class="faq-item">
                    <h4 class="faq-question">❓ 同步速度很慢怎么办？</h4>
                    <div class="faq-answer">
                        <p>可以尝试以下优化方法：</p>
                        <ul>
                            <li>在 "其他设置" 中启用性能优化模式</li>
                            <li>适当增加 API 分页大小和并发请求数</li>
                            <li>使用智能同步而非完全同步</li>
                            <li>在 "性能监控" 中查看详细的性能数据</li>
                        </ul>
                    </div>
                </div>

                <div class="faq-item">
                    <h4 class="faq-question">❓ 如何处理图片和媒体文件？</h4>
                    <div class="faq-answer">
                        <p>插件支持多种媒体文件处理：</p>
                        <ul>
                            <li>自动下载并上传到 WordPress 媒体库</li>
                            <li>支持 JPG、PNG、GIF、WebP 等格式</li>
                            <li>可设置最大图片尺寸限制</li>
                            <li>支持 CDN 加速和压缩优化</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取详细说明内容
     */
    getDetailedGuideContent() {
        return `
            <div class="help-content-section">
                <h4>📋 字段映射详解</h4>
                <p>字段映射是插件的核心功能，用于定义 Notion 属性与 WordPress 字段的对应关系：</p>
                <ul>
                    <li><strong>基础字段映射：</strong>标题、状态、文章类型、日期、摘要、特色图片、分类、标签、密码</li>
                    <li><strong>自定义字段映射：</strong>支持任意 Notion 属性映射到 WordPress 自定义字段</li>
                    <li><strong>字段类型支持：</strong>文本、数字、日期、复选框、选择、多选、URL、邮箱、电话、富文本</li>
                </ul>

                <h4>⚙️ 高级设置说明</h4>
                <p>在 "其他设置" 中可以配置更多高级选项：</p>
                <ul>
                    <li><strong>性能优化：</strong>API 分页大小、并发请求数、批量处理大小</li>
                    <li><strong>前端优化：</strong>资源压缩、懒加载、性能监控</li>
                    <li><strong>CDN 配置：</strong>支持 jsDelivr 等 CDN 服务</li>
                    <li><strong>安全设置：</strong>iframe 白名单、图片格式限制</li>
                </ul>

                <h4>📊 性能监控功能</h4>
                <p>性能监控标签页提供详细的运行状态信息：</p>
                <ul>
                    <li><strong>异步处理状态：</strong>查看当前同步任务的进度和状态</li>
                    <li><strong>队列管理：</strong>监控任务队列的执行情况</li>
                    <li><strong>数据库优化：</strong>创建索引和优化表结构</li>
                    <li><strong>系统信息：</strong>PHP 版本、内存使用等环境信息</li>
                </ul>

                <h4>🐞 调试工具使用</h4>
                <p>调试工具帮助您诊断和解决问题：</p>
                <ul>
                    <li><strong>日志级别：</strong>设置日志记录的详细程度</li>
                    <li><strong>日志查看：</strong>实时查看错误和调试信息</li>
                    <li><strong>日志管理：</strong>清理旧日志和设置保留期限</li>
                </ul>
            </div>
        `;
    }

    /**
     * 获取故障排除内容
     */
    getTroubleshootingContent() {
        return `
            <div class="help-content-section">
                <h4>🔍 常见错误诊断</h4>
                
                <div class="troubleshooting-item">
                    <h5>❌ API 连接失败</h5>
                    <p><strong>症状：</strong>测试连接时显示连接失败</p>
                    <p><strong>解决方案：</strong></p>
                    <ul>
                        <li>检查 API 密钥是否正确（以 secret_ 开头）</li>
                        <li>确认数据库 ID 格式正确（32位字符串）</li>
                        <li>验证 Notion 集成是否已与数据库共享</li>
                        <li>检查服务器网络连接是否正常</li>
                    </ul>
                </div>

                <div class="troubleshooting-item">
                    <h5>⏱️ 同步超时</h5>
                    <p><strong>症状：</strong>同步过程中出现超时错误</p>
                    <p><strong>解决方案：</strong></p>
                    <ul>
                        <li>启用异步处理模式</li>
                        <li>减少 API 分页大小</li>
                        <li>降低并发请求数</li>
                        <li>联系主机商增加 PHP 执行时间限制</li>
                    </ul>
                </div>

                <div class="troubleshooting-item">
                    <h5>🖼️ 图片导入失败</h5>
                    <p><strong>症状：</strong>文章导入成功但图片显示异常</p>
                    <p><strong>解决方案：</strong></p>
                    <ul>
                        <li>检查图片格式是否在允许列表中</li>
                        <li>确认图片大小未超过限制</li>
                        <li>验证 WordPress 媒体库权限</li>
                        <li>检查服务器磁盘空间是否充足</li>
                    </ul>
                </div>

                <div class="troubleshooting-item">
                    <h5>🔄 同步不完整</h5>
                    <p><strong>症状：</strong>只导入了部分内容</p>
                    <p><strong>解决方案：</strong></p>
                    <ul>
                        <li>检查字段映射配置是否完整</li>
                        <li>确认 Notion 页面状态符合同步条件</li>
                        <li>查看调试日志了解具体错误</li>
                        <li>尝试使用完全同步模式</li>
                    </ul>
                </div>

                <h4>🛠️ 诊断工具</h4>
                <p>使用以下工具帮助诊断问题：</p>
                <ul>
                    <li><strong>连接测试：</strong>在 API 设置中测试 Notion 连接</li>
                    <li><strong>调试日志：</strong>在调试工具中查看详细错误信息</li>
                    <li><strong>性能监控：</strong>监控同步过程的性能指标</li>
                    <li><strong>系统检查：</strong>验证服务器环境和配置</li>
                </ul>
            </div>
        `;
    }

    /**
     * 获取高级用法内容
     */
    getAdvancedUsageContent() {
        return `
            <div class="help-content-section">
                <h4>🎯 高级字段映射</h4>
                <p>利用高级字段映射功能实现复杂的内容处理：</p>
                <ul>
                    <li><strong>条件映射：</strong>根据 Notion 属性值动态设置 WordPress 字段</li>
                    <li><strong>格式转换：</strong>自动转换日期格式、数字格式等</li>
                    <li><strong>多值处理：</strong>处理多选字段和关联数据库</li>
                    <li><strong>自定义处理：</strong>使用 WordPress 钩子自定义处理逻辑</li>
                </ul>

                <h4>⚡ 性能优化技巧</h4>
                <p>针对大量内容的性能优化策略：</p>
                <ul>
                    <li><strong>批量处理：</strong>合理设置批量大小平衡速度和稳定性</li>
                    <li><strong>增量同步：</strong>使用智能同步只处理变化的内容</li>
                    <li><strong>异步处理：</strong>启用后台异步处理避免超时</li>
                    <li><strong>缓存策略：</strong>合理使用缓存减少 API 调用</li>
                </ul>

                <h4>🔧 自定义开发</h4>
                <p>为开发者提供的扩展接口：</p>
                <ul>
                    <li><strong>WordPress 钩子：</strong>使用 action 和 filter 钩子自定义行为</li>
                    <li><strong>API 扩展：</strong>扩展 Notion API 调用和数据处理</li>
                    <li><strong>模板系统：</strong>自定义内容渲染模板</li>
                    <li><strong>插件集成：</strong>与其他 WordPress 插件的集成方案</li>
                </ul>

                <h4>📈 监控和分析</h4>
                <p>深入了解插件运行状况：</p>
                <ul>
                    <li><strong>性能指标：</strong>监控同步速度、成功率等关键指标</li>
                    <li><strong>错误分析：</strong>分析错误模式和频率</li>
                    <li><strong>使用统计：</strong>了解内容同步的使用情况</li>
                    <li><strong>优化建议：</strong>基于数据提供优化建议</li>
                </ul>
            </div>
        `;
    }

    /**
     * 获取支持内容
     */
    getSupportContent() {
        return `
            <div class="help-content-section">
                <h4>📞 获取帮助</h4>
                <p>如果您遇到任何问题或需要帮助，可以通过以下方式获取支持：</p>

                <div class="support-channels">
                    <div class="support-item">
                        <h5>🐛 GitHub Issues</h5>
                        <p>报告 bug 或请求新功能</p>
                        <a href="https://github.com/Frank-Loong/Notion-to-WordPress/issues" target="_blank" class="support-link">
                            访问 GitHub Issues
                        </a>
                    </div>

                    <div class="support-item">
                        <h5>📧 邮件支持</h5>
                        <p>发送详细问题描述到开发者邮箱</p>
                        <a href="mailto:<EMAIL>" class="support-link">
                            发送邮件
                        </a>
                    </div>

                    <div class="support-item">
                        <h5>🌐 官方网站</h5>
                        <p>查看更多文档和教程</p>
                        <a href="https://frankloong.com" target="_blank" class="support-link">
                            访问官网
                        </a>
                    </div>
                </div>

                <h4>📋 报告问题时请提供</h4>
                <ul>
                    <li>WordPress 版本和插件版本</li>
                    <li>PHP 版本和服务器环境信息</li>
                    <li>详细的错误描述和重现步骤</li>
                    <li>相关的错误日志（从调试工具获取）</li>
                    <li>Notion 数据库结构截图（如涉及字段映射问题）</li>
                </ul>

                <h4>🤝 贡献代码</h4>
                <p>欢迎开发者参与项目贡献：</p>
                <ul>
                    <li>Fork 项目仓库</li>
                    <li>创建功能分支</li>
                    <li>提交 Pull Request</li>
                    <li>参与代码审查</li>
                </ul>

                <div class="help-tip">
                    <strong>💡 提示：</strong>在寻求帮助之前，请先查看常见问题和故障排除部分，大多数问题都能找到解决方案。
                </div>
            </div>
        `;
    }

    /**
     * 初始化搜索功能
     */
    initSearchFunction() {
        // 创建搜索界面
        this.createSearchInterface();

        // 绑定搜索事件
        this.bindSearchEvents();

        if (this.config.debugMode) {
            console.log('NotionHelpDocs: 搜索功能已初始化');
        }
    }

    /**
     * 创建搜索界面
     */
    createSearchInterface() {
        const settingsSection = this.elements.settingsSection;
        if (!settingsSection) return;

        // 创建搜索容器
        const searchContainer = document.createElement('div');
        searchContainer.className = 'help-search-container';
        searchContainer.innerHTML = `
            <div class="help-search-bar">
                <div class="search-input-wrapper">
                    <input type="text"
                           id="help-search-input"
                           class="help-search-input"
                           placeholder="搜索帮助内容..."
                           autocomplete="off">
                    <span class="search-icon">🔍</span>
                    <button type="button" class="search-clear-btn" id="search-clear-btn" style="display: none;">✕</button>
                </div>
                <div class="search-filters">
                    <label for="section-filter">筛选分类:</label>
                    <select id="section-filter" class="section-filter">
                        <option value="">全部分类</option>
                        <option value="quick-start">快速开始</option>
                        <option value="faq">常见问题</option>
                        <option value="detailed-guide">详细说明</option>
                        <option value="troubleshooting">故障排除</option>
                        <option value="advanced-usage">高级用法</option>
                        <option value="support">获取支持</option>
                    </select>
                </div>
            </div>
            <div class="search-results-info" id="search-results-info" style="display: none;"></div>
        `;

        // 插入到标题后面
        const title = settingsSection.querySelector('h2');
        if (title) {
            title.insertAdjacentElement('afterend', searchContainer);
        } else {
            settingsSection.insertBefore(searchContainer, settingsSection.firstChild);
        }
    }

    /**
     * 绑定搜索事件
     */
    bindSearchEvents() {
        const searchInput = document.getElementById('help-search-input');
        const clearBtn = document.getElementById('search-clear-btn');
        const sectionFilter = document.getElementById('section-filter');

        if (searchInput) {
            // 搜索输入事件
            this.addEventListenerManaged(searchInput, 'input', (e) => {
                const searchTerm = e.target.value.trim();
                this.setState({ searchTerm });
                this.performSearch(searchTerm);

                // 显示/隐藏清除按钮
                if (clearBtn) {
                    clearBtn.style.display = searchTerm ? 'block' : 'none';
                }
            });

            // 搜索框焦点事件
            this.addEventListenerManaged(searchInput, 'focus', () => {
                searchInput.parentElement.classList.add('focused');
            });

            this.addEventListenerManaged(searchInput, 'blur', () => {
                searchInput.parentElement.classList.remove('focused');
            });
        }

        if (clearBtn) {
            // 清除搜索按钮
            this.addEventListenerManaged(clearBtn, 'click', () => {
                searchInput.value = '';
                this.setState({ searchTerm: '' });
                this.clearSearch();
                clearBtn.style.display = 'none';
                searchInput.focus();
            });
        }

        if (sectionFilter) {
            // 分类筛选
            this.addEventListenerManaged(sectionFilter, 'change', (e) => {
                this.filterBySection(e.target.value);
            });
        }
    }

    /**
     * 执行搜索
     */
    performSearch(searchTerm) {
        if (!searchTerm) {
            this.clearSearch();
            return;
        }

        const filteredSections = this.state.helpSections.filter(section => {
            const titleMatch = section.title.toLowerCase().includes(searchTerm.toLowerCase());
            const contentMatch = section.content.toLowerCase().includes(searchTerm.toLowerCase());
            return titleMatch || contentMatch;
        });

        this.setState({ filteredSections });
        this.updateSearchResults(searchTerm, filteredSections);
        this.highlightSearchTerms(searchTerm);

        if (this.config.debugMode) {
            console.log('NotionHelpDocs: 搜索结果', { searchTerm, results: filteredSections.length });
        }
    }

    /**
     * 清除搜索
     */
    clearSearch() {
        this.setState({
            searchTerm: '',
            filteredSections: this.state.helpSections
        });
        this.updateSearchResults('', this.state.helpSections);
        this.removeHighlights();

        // 隐藏搜索结果信息
        const resultsInfo = document.getElementById('search-results-info');
        if (resultsInfo) {
            resultsInfo.style.display = 'none';
        }
    }

    /**
     * 按分类筛选
     */
    filterBySection(sectionId) {
        if (!sectionId) {
            this.setState({ filteredSections: this.state.helpSections });
            this.updateSearchResults('', this.state.helpSections);
            return;
        }

        const filteredSections = this.state.helpSections.filter(section => section.id === sectionId);
        this.setState({ filteredSections });
        this.updateSearchResults('', filteredSections, `分类: ${this.getSectionTitle(sectionId)}`);
    }

    /**
     * 获取分类标题
     */
    getSectionTitle(sectionId) {
        const section = this.state.helpSections.find(s => s.id === sectionId);
        return section ? section.title : sectionId;
    }

    /**
     * 更新搜索结果显示
     */
    updateSearchResults(searchTerm, results, filterInfo = '') {
        const resultsInfo = document.getElementById('search-results-info');
        if (!resultsInfo) return;

        if (searchTerm || filterInfo) {
            let infoText = '';
            if (searchTerm) {
                infoText = `搜索 "${searchTerm}" 找到 ${results.length} 个结果`;
            } else if (filterInfo) {
                infoText = `${filterInfo} - ${results.length} 个结果`;
            }

            resultsInfo.innerHTML = `
                <div class="search-results-summary">
                    <span class="results-text">${infoText}</span>
                    ${results.length === 0 ? '<span class="no-results">未找到匹配的内容</span>' : ''}
                </div>
            `;
            resultsInfo.style.display = 'block';
        } else {
            resultsInfo.style.display = 'none';
        }

        // 显示/隐藏相关章节
        this.toggleSectionsVisibility(results);
    }

    /**
     * 切换章节可见性
     */
    toggleSectionsVisibility(visibleSections) {
        const allSectionIds = this.state.helpSections.map(s => s.id);
        const visibleSectionIds = visibleSections.map(s => s.id);

        allSectionIds.forEach(sectionId => {
            const sectionElement = this.querySelector(`[data-section-id="${sectionId}"]`);
            if (sectionElement) {
                const isVisible = visibleSectionIds.includes(sectionId);
                sectionElement.style.display = isVisible ? 'block' : 'none';
            }
        });
    }

    /**
     * 高亮搜索词
     */
    highlightSearchTerms(searchTerm) {
        if (!searchTerm) return;

        const sections = this.querySelectorAll('.help-content-section');
        sections.forEach(section => {
            this.highlightInElement(section, searchTerm);
        });
    }

    /**
     * 在元素中高亮文本
     */
    highlightInElement(element, searchTerm) {
        const walker = document.createTreeWalker(
            element,
            NodeFilter.SHOW_TEXT,
            null,
            false
        );

        const textNodes = [];
        let node;
        while (node = walker.nextNode()) {
            textNodes.push(node);
        }

        textNodes.forEach(textNode => {
            const text = textNode.textContent;
            const regex = new RegExp(`(${this.escapeRegExp(searchTerm)})`, 'gi');

            if (regex.test(text)) {
                const highlightedText = text.replace(regex, '<mark class="search-highlight">$1</mark>');
                const wrapper = document.createElement('span');
                wrapper.innerHTML = highlightedText;
                textNode.parentNode.replaceChild(wrapper, textNode);
            }
        });
    }

    /**
     * 移除高亮
     */
    removeHighlights() {
        const highlights = this.querySelectorAll('.search-highlight');
        highlights.forEach(highlight => {
            const parent = highlight.parentNode;
            parent.replaceChild(document.createTextNode(highlight.textContent), highlight);
            parent.normalize();
        });
    }

    /**
     * 转义正则表达式特殊字符
     */
    escapeRegExp(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * 切换章节展开/折叠
     */
    toggleSection(sectionId) {
        const expandedSections = new Set(this.state.expandedSections);

        if (expandedSections.has(sectionId)) {
            expandedSections.delete(sectionId);
        } else {
            expandedSections.add(sectionId);
        }

        this.setState({
            expandedSections,
            activeSection: sectionId
        });

        this.updateSectionDisplay(sectionId);

        if (this.config.debugMode) {
            console.log('NotionHelpDocs: 切换章节', { sectionId, expanded: expandedSections.has(sectionId) });
        }
    }

    /**
     * 更新章节显示状态
     */
    updateSectionDisplay(sectionId) {
        const sectionElement = this.querySelector(`[data-section-id="${sectionId}"]`);
        if (!sectionElement) return;

        const isExpanded = this.state.expandedSections.has(sectionId);
        const content = sectionElement.querySelector('.help-section-content');
        const toggle = sectionElement.querySelector('.section-toggle');

        if (content) {
            content.style.display = isExpanded ? 'block' : 'none';
        }

        if (toggle) {
            toggle.textContent = isExpanded ? '▼' : '▶';
            toggle.setAttribute('aria-expanded', isExpanded);
        }

        sectionElement.classList.toggle('expanded', isExpanded);
        sectionElement.classList.toggle('collapsed', !isExpanded);
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 标签页切换监听
        this.bindTabSwitchListener();

        // 章节切换事件
        this.bindSectionToggleEvents();

        // 导航事件
        this.bindNavigationEvents();
    }

    /**
     * 绑定标签页切换监听
     */
    bindTabSwitchListener() {
        // 监听标签页切换事件
        this.subscribe('tab:switched', (data) => {
            if (data.tabId === 'help') {
                // 当切换到帮助文档标签页时，重新渲染内容
                this.render();
            }
        });
    }

    /**
     * 绑定章节切换事件
     */
    bindSectionToggleEvents() {
        // 使用事件委托处理章节切换
        if (this.elements.helpTab) {
            this.addEventListenerManaged(this.elements.helpTab, 'click', (e) => {
                const toggleBtn = e.target.closest('.section-toggle-btn');
                if (toggleBtn) {
                    const sectionId = toggleBtn.getAttribute('data-section-id');
                    if (sectionId) {
                        this.toggleSection(sectionId);
                    }
                }
            });
        }
    }

    /**
     * 绑定导航事件
     */
    bindNavigationEvents() {
        // 快速导航链接
        if (this.elements.helpTab) {
            this.addEventListenerManaged(this.elements.helpTab, 'click', (e) => {
                const navLink = e.target.closest('.help-nav-link');
                if (navLink) {
                    e.preventDefault();
                    const targetSection = navLink.getAttribute('data-target');
                    if (targetSection) {
                        this.scrollToSection(targetSection);
                        this.setState({ activeSection: targetSection });
                    }
                }
            });
        }
    }

    /**
     * 滚动到指定章节
     */
    scrollToSection(sectionId) {
        const sectionElement = this.querySelector(`[data-section-id="${sectionId}"]`);
        if (sectionElement) {
            // 确保章节是展开的
            if (!this.state.expandedSections.has(sectionId)) {
                this.toggleSection(sectionId);
            }

            // 滚动到章节
            sectionElement.scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });

            // 高亮章节
            sectionElement.classList.add('highlighted');
            setTimeout(() => {
                sectionElement.classList.remove('highlighted');
            }, 2000);
        }
    }

    /**
     * 渲染组件增强
     */
    render() {
        // 如果组件容器不存在，创建增强显示
        if (!this.querySelector('.help-enhanced-container')) {
            this.createEnhancedHelpInterface();
        }

        // 更新章节显示
        this.updateAllSectionsDisplay();

        // 更新导航
        this.updateNavigation();
    }

    /**
     * 创建增强的帮助界面
     */
    createEnhancedHelpInterface() {
        const settingsSection = this.elements.settingsSection;
        if (!settingsSection) return;

        // 创建增强容器
        const enhancedContainer = document.createElement('div');
        enhancedContainer.className = 'help-enhanced-container';

        // 创建导航栏
        const navigation = this.createNavigationBar();
        enhancedContainer.appendChild(navigation);

        // 创建内容区域
        const contentArea = this.createContentArea();
        enhancedContainer.appendChild(contentArea);

        // 插入到设置区域
        settingsSection.appendChild(enhancedContainer);

        if (this.config.debugMode) {
            console.log('NotionHelpDocs: 已创建增强界面');
        }
    }

    /**
     * 创建导航栏
     */
    createNavigationBar() {
        const nav = document.createElement('nav');
        nav.className = 'help-navigation';

        const navItems = this.state.helpSections.map(section => {
            return `
                <a href="#"
                   class="help-nav-link ${section.id === this.state.activeSection ? 'active' : ''}"
                   data-target="${section.id}">
                    <span class="nav-icon">${section.icon}</span>
                    <span class="nav-text">${section.title.replace(/^[🚀❓📖🔧⚡💬]\s*/, '')}</span>
                </a>
            `;
        }).join('');

        nav.innerHTML = `
            <div class="nav-header">
                <h4>📚 快速导航</h4>
            </div>
            <div class="nav-items">
                ${navItems}
            </div>
        `;

        return nav;
    }

    /**
     * 创建内容区域
     */
    createContentArea() {
        const contentArea = document.createElement('div');
        contentArea.className = 'help-content-area';

        const sectionsHtml = this.state.helpSections.map(section => {
            const isExpanded = this.state.expandedSections.has(section.id);
            return `
                <div class="help-section ${isExpanded ? 'expanded' : 'collapsed'}"
                     data-section-id="${section.id}">
                    <div class="help-section-header">
                        <button type="button"
                                class="section-toggle-btn"
                                data-section-id="${section.id}"
                                aria-expanded="${isExpanded}">
                            <span class="section-icon">${section.icon}</span>
                            <h3 class="section-title">${section.title}</h3>
                            <span class="section-toggle">${isExpanded ? '▼' : '▶'}</span>
                        </button>
                    </div>
                    <div class="help-section-content" style="display: ${isExpanded ? 'block' : 'none'};">
                        ${section.content}
                    </div>
                </div>
            `;
        }).join('');

        contentArea.innerHTML = sectionsHtml;
        return contentArea;
    }

    /**
     * 更新所有章节显示
     */
    updateAllSectionsDisplay() {
        this.state.helpSections.forEach(section => {
            this.updateSectionDisplay(section.id);
        });
    }

    /**
     * 更新导航
     */
    updateNavigation() {
        const navLinks = this.querySelectorAll('.help-nav-link');
        navLinks.forEach(link => {
            const target = link.getAttribute('data-target');
            link.classList.toggle('active', target === this.state.activeSection);
        });
    }

    /**
     * 检查组件是否可见
     */
    isVisible() {
        const helpTab = this.elements.helpTab;
        if (!helpTab) return false;

        // 检查标签页是否激活
        return helpTab.classList.contains('active') ||
               helpTab.style.display !== 'none';
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        this.setState({ error: null });

        // 创建临时成功提示
        const successDiv = document.createElement('div');
        successDiv.className = 'help-success notice notice-success is-dismissible';
        successDiv.innerHTML = `
            <p><strong>成功:</strong> ${this.escapeHtml(message)}</p>
            <button type="button" class="notice-dismiss">
                <span class="screen-reader-text">忽略此通知</span>
            </button>
        `;

        // 插入到帮助文档区域顶部
        const settingsSection = this.elements.settingsSection;
        if (settingsSection) {
            settingsSection.insertBefore(successDiv, settingsSection.firstChild);
        }

        // 3秒后自动移除
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.remove();
            }
        }, 3000);
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        this.setState({ error: message });

        // 创建错误提示
        const errorDiv = document.createElement('div');
        errorDiv.className = 'help-error notice notice-error is-dismissible';
        errorDiv.innerHTML = `
            <p><strong>错误:</strong> ${this.escapeHtml(message)}</p>
            <button type="button" class="notice-dismiss">
                <span class="screen-reader-text">忽略此通知</span>
            </button>
        `;

        // 插入到帮助文档区域顶部
        const settingsSection = this.elements.settingsSection;
        if (settingsSection) {
            settingsSection.insertBefore(errorDiv, settingsSection.firstChild);
        }
    }

    /**
     * 处理错误
     */
    handleError(message, error) {
        console.error(message, error);
        this.showError(`${message}: ${error.message || error}`);
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        if (typeof text !== 'string') return text;

        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 获取帮助统计信息
     */
    getHelpStats() {
        return {
            totalSections: this.state.helpSections.length,
            expandedSections: this.state.expandedSections.size,
            searchResults: this.state.filteredSections.length,
            activeSection: this.state.activeSection,
            hasSearchTerm: !!this.state.searchTerm
        };
    }

    /**
     * 导出帮助内容
     */
    exportHelpContent() {
        const content = this.state.helpSections.map(section => {
            return `# ${section.title}\n\n${this.stripHtml(section.content)}\n\n---\n\n`;
        }).join('');

        const blob = new Blob([content], { type: 'text/markdown' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `notion-wp-help-${new Date().toISOString().split('T')[0]}.md`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        URL.revokeObjectURL(url);
        this.showSuccess('帮助文档已导出');
    }

    /**
     * 移除HTML标签
     */
    stripHtml(html) {
        const div = document.createElement('div');
        div.innerHTML = html;
        return div.textContent || div.innerText || '';
    }

    /**
     * 重置帮助界面
     */
    reset() {
        // 重置状态
        this.setState({
            searchTerm: '',
            filteredSections: this.state.helpSections,
            activeSection: 'quick-start',
            expandedSections: new Set(['quick-start']),
            error: null
        });

        // 清除搜索
        const searchInput = document.getElementById('help-search-input');
        if (searchInput) {
            searchInput.value = '';
        }

        // 重新渲染
        this.render();

        if (this.config.debugMode) {
            console.log('NotionHelpDocs: 界面已重置');
        }
    }

    cleanup() {
        // 移除高亮
        this.removeHighlights();

        // 清理DOM元素引用
        this.elements = {};

        // 调用父类清理方法
        super.cleanup();

        if (this.config.debugMode) {
            console.log('NotionHelpDocs: 组件已清理');
        }
    }
}

// 注册组件
if (window.NotionRegistry) {
    window.NotionRegistry.register('notion-help-docs', NotionHelpDocs);
} else {
    console.error('NotionRegistry not found. Make sure component-registry.js is loaded first.');
}
