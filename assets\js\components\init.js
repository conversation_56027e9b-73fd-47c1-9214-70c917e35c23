/**
 * Notion to WordPress - 组件初始化脚本
 * 确保所有Web Components正确加载和初始化
 * 现在作为主应用系统的兼容层
 */

(function() {
    'use strict';

    // 兼容性检查和初始化
    function initializeCompatibilityLayer() {
        console.log('Notion Components: Starting compatibility layer...');

        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeCompatibilityLayer);
            return;
        }

        // 检查主应用是否已加载
        if (window.NotionAdminApp) {
            console.log('Notion Components: Main app detected, using app-based initialization');

            // 等待主应用初始化完成
            const checkAppReady = () => {
                if (window.NotionAdminApp.initialized) {
                    console.log('Notion Components: Main app initialized, compatibility layer ready');
                    setupLegacySupport();
                } else {
                    setTimeout(checkAppReady, 100);
                }
            };
            checkAppReady();

        } else {
            console.log('Notion Components: Main app not found, using legacy initialization');
            initializeLegacyMode();
        }
    }

    // 设置遗留支持
    function setupLegacySupport() {
        // 为旧代码提供兼容接口
        if (!window.NotionComponentsLegacy) {
            window.NotionComponentsLegacy = {
                initialized: true,

                // 兼容旧的初始化检查
                checkDependencies: function() {
                    return window.NotionAdminApp ? window.NotionAdminApp.initialized : false;
                },

                // 兼容旧的组件检查
                checkComponents: function() {
                    if (!window.NotionAdminApp) return [];

                    const status = window.NotionAdminApp.getStatus();
                    return Object.keys(status.components);
                },

                // 兼容旧的实例查找
                findInstances: function(componentName) {
                    return document.querySelectorAll(componentName);
                },

                // 兼容旧的重新初始化
                reinitialize: function() {
                    if (window.NotionAdminApp) {
                        return window.NotionAdminApp.reinitialize();
                    }
                }
            };
        }
    }

    // 遗留模式初始化（当主应用不可用时）
    function initializeLegacyMode() {
        console.log('Notion Components: Running in legacy mode');

        // 检查必要的依赖
        if (!checkBasicDependencies()) {
            console.error('Notion Components: Cannot initialize due to missing dependencies');
            return;
        }

        // 基本的组件检查
        const knownComponents = [
            'notion-tab-manager',
            'notion-api-settings',
            'notion-field-mapping',
            'notion-other-settings',
            'notion-performance-monitor',
            'notion-debug-tools',
            'notion-help-docs',
            'notion-about-author'
        ];

        let foundComponents = 0;
        knownComponents.forEach(componentName => {
            const elements = document.querySelectorAll(componentName);
            if (elements.length > 0) {
                foundComponents++;
                console.log(`✓ Found ${elements.length} instances of ${componentName}`);
            }
        });

        console.log(`Notion Components: Legacy initialization complete - found ${foundComponents} component types`);

        // 设置基本错误处理
        setupBasicErrorHandling();
    }

    // 检查基本依赖
    function checkBasicDependencies() {
        const dependencies = [
            { name: 'Custom Elements', check: () => 'customElements' in window },
            { name: 'ES6 Classes', check: () => {
                try {
                    eval('class TestClass {}');
                    return true;
                } catch (e) {
                    return false;
                }
            }},
            { name: 'Fetch API', check: () => 'fetch' in window },
            { name: 'Promise', check: () => 'Promise' in window }
        ];

        const missing = dependencies.filter(dep => !dep.check());

        if (missing.length > 0) {
            console.error('Notion Components: Missing basic dependencies:', missing.map(d => d.name));
            return false;
        }

        return true;
    }

    // 设置基本错误处理
    function setupBasicErrorHandling() {
        // 全局JavaScript错误处理
        window.addEventListener('error', function(e) {
            if (e.filename && e.filename.includes('components/')) {
                console.error('Notion Components Error:', e.error);
            }
        });

        // 设置未处理的Promise错误处理
        window.addEventListener('unhandledrejection', function(e) {
            if (e.reason && e.reason.stack && e.reason.stack.includes('components/')) {
                console.error('Notion Components Promise Error:', e.reason);
            }
        });
    }

    // 提供增强的调试工具
    window.NotionComponentsDebug = {
        // 获取注册器统计信息
        getRegistryStats: function() {
            if (window.NotionAdminApp && window.NotionAdminApp.initialized) {
                return window.NotionAdminApp.getStatus();
            }
            return window.NotionRegistry ? window.NotionRegistry.getStats() : null;
        },

        // 列出所有组件
        listComponents: function() {
            if (window.NotionAdminApp && window.NotionAdminApp.initialized) {
                const status = window.NotionAdminApp.getStatus();
                return Object.keys(status.components);
            }
            if (!window.NotionRegistry) return [];
            const stats = window.NotionRegistry.getStats();
            return Object.keys(stats.components);
        },

        // 获取组件实例
        getComponentInstances: function(componentName) {
            if (window.NotionRegistry) {
                return window.NotionRegistry.getInstances(componentName);
            }
            return Array.from(document.querySelectorAll(componentName));
        },

        // 检查系统健康状态
        checkHealth: function() {
            const health = {
                mainAppLoaded: !!window.NotionAdminApp,
                mainAppInitialized: window.NotionAdminApp ? window.NotionAdminApp.initialized : false,
                registryLoaded: !!window.NotionRegistry,
                baseComponentLoaded: typeof window.NotionBaseComponent === 'function',
                customElementsSupported: 'customElements' in window,
                componentsRegistered: 0,
                componentInstances: 0,
                mode: 'unknown'
            };

            if (window.NotionAdminApp && window.NotionAdminApp.initialized) {
                const status = window.NotionAdminApp.getStatus();
                health.componentsRegistered = Object.keys(status.components).length;
                health.componentInstances = status.totalComponentInstances;
                health.mode = 'app-based';
            } else if (window.NotionRegistry) {
                const stats = window.NotionRegistry.getStats();
                health.componentsRegistered = stats.totalComponents;
                health.componentInstances = stats.totalInstances;
                health.mode = 'registry-based';
            } else {
                health.mode = 'legacy';
            }

            return health;
        },

        // 重新初始化
        reinitialize: function() {
            console.log('Notion Components: Manual reinitialization requested');

            if (window.NotionAdminApp) {
                return window.NotionAdminApp.reinitialize();
            } else if (window.NotionComponentsLegacy) {
                return window.NotionComponentsLegacy.reinitialize();
            } else {
                console.warn('Notion Components: No reinitialization method available');
            }
        },

        // 获取详细状态
        getDetailedStatus: function() {
            const health = this.checkHealth();
            const components = this.listComponents();
            const instances = {};

            components.forEach(componentName => {
                instances[componentName] = this.getComponentInstances(componentName).length;
            });

            return {
                health,
                components: instances,
                services: window.notionServices ? Object.keys(window.notionServices) : [],
                debugMode: window.notionToWp?.debug || false,
                version: window.notionToWp?.version || 'unknown'
            };
        }
    };

    // 自动初始化兼容层
    initializeCompatibilityLayer();

    // 在控制台显示调试信息
    console.log('%c🔧 Notion to WordPress Components Init', 'color: #059669; font-weight: bold; font-size: 14px;');
    console.log('%cCompatibility layer loaded - Use NotionComponentsDebug for debugging', 'color: #6b7280; font-size: 12px;');

})();
