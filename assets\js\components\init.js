/**
 * Notion to WordPress - 组件初始化脚本
 * 确保所有Web Components正确加载和初始化
 */

(function() {
    'use strict';

    // 检查必要的依赖
    function checkDependencies() {
        const dependencies = [
            { name: 'Custom Elements', check: () => 'customElements' in window },
            { name: 'ES6 Classes', check: () => {
                try {
                    eval('class TestClass {}');
                    return true;
                } catch (e) {
                    return false;
                }
            }},
            { name: 'Fetch API', check: () => 'fetch' in window },
            { name: 'Promise', check: () => 'Promise' in window },
            { name: 'NotionBaseComponent', check: () => typeof window.NotionBaseComponent === 'function' },
            { name: 'NotionRegistry', check: () => window.NotionRegistry && typeof window.NotionRegistry.register === 'function' }
        ];

        const missing = dependencies.filter(dep => !dep.check());
        
        if (missing.length > 0) {
            console.error('Notion Components: Missing dependencies:', missing.map(d => d.name));
            return false;
        }

        console.log('Notion Components: All dependencies satisfied');
        return true;
    }

    // 初始化组件系统
    function initializeComponents() {
        if (!checkDependencies()) {
            console.error('Notion Components: Cannot initialize due to missing dependencies');
            return;
        }

        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', initializeComponents);
            return;
        }

        console.log('Notion Components: Initializing component system...');

        // 检查组件注册状态
        const registeredComponents = [
            'notion-tab-manager',
            'notion-performance-monitor'
        ];

        registeredComponents.forEach(componentName => {
            if (window.NotionRegistry.has(componentName)) {
                console.log(`✓ Component ${componentName} registered successfully`);
            } else {
                console.warn(`⚠ Component ${componentName} not registered`);
            }
        });

        // 扫描页面中的组件实例
        const componentInstances = document.querySelectorAll(registeredComponents.join(','));
        console.log(`Found ${componentInstances.length} component instances on page`);

        // 设置全局错误处理
        window.addEventListener('error', function(e) {
            if (e.filename && e.filename.includes('components/')) {
                console.error('Notion Components Error:', e.error);
            }
        });

        // 设置未处理的Promise错误处理
        window.addEventListener('unhandledrejection', function(e) {
            if (e.reason && e.reason.stack && e.reason.stack.includes('components/')) {
                console.error('Notion Components Promise Error:', e.reason);
            }
        });

        console.log('Notion Components: Initialization complete');
    }

    // 提供调试工具
    window.NotionComponentsDebug = {
        getRegistryStats: function() {
            return window.NotionRegistry ? window.NotionRegistry.getStats() : null;
        },
        
        listComponents: function() {
            if (!window.NotionRegistry) return [];
            const stats = window.NotionRegistry.getStats();
            return Object.keys(stats.components);
        },
        
        getComponentInstances: function(componentName) {
            return window.NotionRegistry ? window.NotionRegistry.getInstances(componentName) : [];
        },
        
        checkHealth: function() {
            const health = {
                registryLoaded: !!window.NotionRegistry,
                baseComponentLoaded: typeof window.NotionBaseComponent === 'function',
                customElementsSupported: 'customElements' in window,
                componentsRegistered: 0,
                componentInstances: 0
            };

            if (window.NotionRegistry) {
                const stats = window.NotionRegistry.getStats();
                health.componentsRegistered = stats.totalComponents;
                health.componentInstances = stats.totalInstances;
            }

            return health;
        },
        
        reinitialize: function() {
            console.log('Notion Components: Manual reinitialization requested');
            initializeComponents();
        }
    };

    // 自动初始化
    initializeComponents();

    // 在控制台显示调试信息
    console.log('%c🚀 Notion to WordPress Components Loaded', 'color: #2563eb; font-weight: bold; font-size: 14px;');
    console.log('Use NotionComponentsDebug for debugging tools');

})();
