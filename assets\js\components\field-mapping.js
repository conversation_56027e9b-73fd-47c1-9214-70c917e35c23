/**
 * Notion to WordPress - 字段映射组件
 * 处理基础字段映射、自定义字段映射和映射模板功能
 */

class NotionFieldMapping extends NotionBaseComponent {
    constructor() {
        super();
        this.state = {
            // 基础字段映射
            basicMappings: {
                title: '',
                status: '',
                post_type: '',
                date: '',
                excerpt: '',
                featured_image: '',
                categories: '',
                tags: '',
                password: ''
            },
            // 自定义字段映射
            customMappings: [],
            // 字段类型选项
            fieldTypes: {
                'text': '文本',
                'number': '数字',
                'date': '日期',
                'checkbox': '复选框',
                'select': '选择',
                'multi_select': '多选',
                'url': 'URL',
                'email': '电子邮件',
                'phone': '电话',
                'rich_text': '富文本'
            },
            isLoading: false,
            hasChanges: false
        };
        
        // 缓存DOM元素引用
        this.elements = {};
    }

    init() {
        // 缓存重要的DOM元素
        this.cacheElements();
        
        // 从现有HTML表单读取数据
        this.loadExistingMappings();
        
        // 绑定事件监听器
        this.bindEvents();
        
        // 渲染组件增强
        this.render();
        
        if (this.config.debugMode) {
            console.log('NotionFieldMapping: 组件初始化完成', this.state);
        }
    }

    /**
     * 缓存DOM元素引用
     */
    cacheElements() {
        this.elements = {
            // 基础字段映射输入
            titleInput: document.getElementById('mapping_title'),
            statusInput: document.getElementById('mapping_status'),
            postTypeInput: document.getElementById('mapping_post_type'),
            dateInput: document.getElementById('mapping_date'),
            excerptInput: document.getElementById('mapping_excerpt'),
            featuredImageInput: document.getElementById('mapping_featured_image'),
            categoriesInput: document.getElementById('mapping_categories'),
            tagsInput: document.getElementById('mapping_tags'),
            passwordInput: document.getElementById('mapping_password'),
            
            // 自定义字段映射容器和按钮
            customFieldsContainer: document.getElementById('custom-field-mappings'),
            addCustomFieldBtn: document.getElementById('add-custom-field'),
            
            // 字段映射标签页容器
            fieldMappingTab: document.getElementById('field-mapping')
        };
    }

    /**
     * 从现有HTML表单读取映射数据
     */
    loadExistingMappings() {
        try {
            // 加载基础字段映射
            const basicMappings = {};
            const basicFields = ['title', 'status', 'post_type', 'date', 'excerpt', 'featured_image', 'categories', 'tags', 'password'];
            
            basicFields.forEach(field => {
                const element = this.elements[field + 'Input'];
                if (element) {
                    basicMappings[field] = element.value || '';
                }
            });
            
            // 加载自定义字段映射
            const customMappings = [];
            if (this.elements.customFieldsContainer) {
                const customFieldRows = this.elements.customFieldsContainer.querySelectorAll('.custom-field-mapping');
                
                customFieldRows.forEach((row, index) => {
                    const notionPropertyInput = row.querySelector('input[name*="[notion_property]"]');
                    const wpFieldInput = row.querySelector('input[name*="[wp_field]"]');
                    const fieldTypeSelect = row.querySelector('select[name*="[field_type]"]');
                    
                    if (notionPropertyInput && wpFieldInput && fieldTypeSelect) {
                        customMappings.push({
                            id: Date.now() + index, // 生成唯一ID
                            notionProperty: notionPropertyInput.value || '',
                            wpField: wpFieldInput.value || '',
                            fieldType: fieldTypeSelect.value || 'text'
                        });
                    }
                });
            }
            
            this.setState({
                basicMappings,
                customMappings
            });
            
            if (this.config.debugMode) {
                console.log('NotionFieldMapping: 已加载现有映射数据', {
                    basicMappings,
                    customMappings
                });
            }
        } catch (error) {
            this.handleError('加载字段映射数据失败', error);
        }
    }

    /**
     * 添加自定义字段映射
     */
    addCustomMapping() {
        const newMapping = {
            id: Date.now(),
            notionProperty: '',
            wpField: '',
            fieldType: 'text'
        };
        
        const customMappings = [...this.state.customMappings, newMapping];
        this.setState({ customMappings, hasChanges: true });
        
        // 更新DOM
        this.renderCustomMapping(newMapping, customMappings.length - 1);
        
        // 确保删除按钮可见性正确
        this.updateRemoveButtonsVisibility();
        
        // 广播变化事件
        this.broadcast('field-mapping:custom-added', {
            mapping: newMapping,
            totalCount: customMappings.length
        });
        
        if (this.config.debugMode) {
            console.log('NotionFieldMapping: 添加自定义映射', newMapping);
        }
    }

    /**
     * 删除自定义字段映射
     */
    removeCustomMapping(id) {
        const customMappings = this.state.customMappings.filter(mapping => mapping.id !== id);
        
        // 至少保留一个映射
        if (customMappings.length === 0) {
            customMappings.push({
                id: Date.now(),
                notionProperty: '',
                wpField: '',
                fieldType: 'text'
            });
        }
        
        this.setState({ customMappings, hasChanges: true });
        
        // 重新渲染自定义映射
        this.renderAllCustomMappings();
        
        // 广播变化事件
        this.broadcast('field-mapping:custom-removed', {
            removedId: id,
            totalCount: customMappings.length
        });
        
        if (this.config.debugMode) {
            console.log('NotionFieldMapping: 删除自定义映射', id);
        }
    }

    /**
     * 更新自定义字段映射
     */
    updateCustomMapping(id, field, value) {
        const customMappings = this.state.customMappings.map(mapping => {
            if (mapping.id === id) {
                return { ...mapping, [field]: value };
            }
            return mapping;
        });
        
        this.setState({ customMappings, hasChanges: true });
        
        // 广播变化事件
        this.broadcast('field-mapping:custom-updated', {
            id,
            field,
            value,
            mapping: customMappings.find(m => m.id === id)
        });
        
        if (this.config.debugMode) {
            console.log('NotionFieldMapping: 更新自定义映射', { id, field, value });
        }
    }

    /**
     * 渲染单个自定义映射
     */
    renderCustomMapping(mapping, index) {
        if (!this.elements.customFieldsContainer) return;
        
        const mappingElement = document.createElement('div');
        mappingElement.className = 'custom-field-mapping';
        mappingElement.setAttribute('data-mapping-id', mapping.id);
        
        mappingElement.innerHTML = `
            <div class="custom-field-row">
                <div class="custom-field-col">
                    <label>Notion属性名称</label>
                    <input type="text" name="custom_field_mappings[${index}][notion_property]" 
                        value="${this.escapeHtml(mapping.notionProperty)}" 
                        class="regular-text notion-property-input" 
                        placeholder="例如：Author,作者"
                        data-mapping-id="${mapping.id}">
                    <p class="description">Notion中的属性名称，多个备选名称请用英文逗号分隔</p>
                </div>
                <div class="custom-field-col">
                    <label>WordPress字段名称</label>
                    <input type="text" name="custom_field_mappings[${index}][wp_field]" 
                        value="${this.escapeHtml(mapping.wpField)}" 
                        class="regular-text wp-field-input" 
                        placeholder="例如：author"
                        data-mapping-id="${mapping.id}">
                    <p class="description">WordPress中的自定义字段名称</p>
                </div>
                <div class="custom-field-col">
                    <label>字段类型</label>
                    <select name="custom_field_mappings[${index}][field_type]" 
                        class="regular-text field-type-select"
                        data-mapping-id="${mapping.id}">
                        ${Object.entries(this.state.fieldTypes).map(([value, label]) => 
                            `<option value="${value}" ${mapping.fieldType === value ? 'selected' : ''}>${label}</option>`
                        ).join('')}
                    </select>
                    <p class="description">Notion属性的数据类型</p>
                </div>
                <div class="custom-field-actions">
                    <button type="button" class="button remove-field" data-mapping-id="${mapping.id}">
                        <span class="dashicons dashicons-trash"></span>
                    </button>
                </div>
            </div>
        `;
        
        this.elements.customFieldsContainer.appendChild(mappingElement);
    }

    /**
     * 重新渲染所有自定义映射
     */
    renderAllCustomMappings() {
        if (!this.elements.customFieldsContainer) return;
        
        // 清空容器
        this.elements.customFieldsContainer.innerHTML = '';
        
        // 渲染所有映射
        this.state.customMappings.forEach((mapping, index) => {
            this.renderCustomMapping(mapping, index);
        });
        
        // 更新删除按钮可见性
        this.updateRemoveButtonsVisibility();
    }

    /**
     * 更新删除按钮的可见性
     */
    updateRemoveButtonsVisibility() {
        const removeButtons = this.elements.customFieldsContainer?.querySelectorAll('.remove-field');
        const shouldShowButtons = this.state.customMappings.length > 1;
        
        if (removeButtons) {
            removeButtons.forEach(button => {
                button.style.display = shouldShowButtons ? 'inline-block' : 'none';
            });
        }
    }

    /**
     * 处理基础字段映射输入变化
     */
    handleBasicMappingChange(field, value) {
        const basicMappings = {
            ...this.state.basicMappings,
            [field]: value
        };
        
        this.setState({ basicMappings, hasChanges: true });
        
        // 广播变化事件
        this.broadcast('field-mapping:basic-updated', {
            field,
            value,
            allMappings: basicMappings
        });
        
        if (this.config.debugMode) {
            console.log('NotionFieldMapping: 基础映射变化', { field, value });
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 基础字段映射输入事件
        const basicFields = ['title', 'status', 'post_type', 'date', 'excerpt', 'featured_image', 'categories', 'tags', 'password'];

        basicFields.forEach(field => {
            const element = this.elements[field + 'Input'];
            if (element) {
                this.addEventListenerManaged(element, 'input', (e) => {
                    this.handleBasicMappingChange(field, e.target.value);
                });
            }
        });

        // 添加自定义字段按钮
        if (this.elements.addCustomFieldBtn) {
            this.addEventListenerManaged(this.elements.addCustomFieldBtn, 'click', (e) => {
                e.preventDefault();
                this.addCustomMapping();
            });
        }

        // 自定义字段映射事件（使用事件委托）
        if (this.elements.customFieldsContainer) {
            // 删除按钮事件
            this.addEventListenerManaged(this.elements.customFieldsContainer, 'click', (e) => {
                if (e.target.classList.contains('remove-field') || e.target.closest('.remove-field')) {
                    e.preventDefault();
                    const button = e.target.closest('.remove-field');
                    const mappingId = parseInt(button.getAttribute('data-mapping-id'));
                    this.removeCustomMapping(mappingId);
                }
            });

            // 输入字段变化事件
            this.addEventListenerManaged(this.elements.customFieldsContainer, 'input', (e) => {
                const target = e.target;
                const mappingId = parseInt(target.getAttribute('data-mapping-id'));

                if (mappingId && target.classList.contains('notion-property-input')) {
                    this.updateCustomMapping(mappingId, 'notionProperty', target.value);
                } else if (mappingId && target.classList.contains('wp-field-input')) {
                    this.updateCustomMapping(mappingId, 'wpField', target.value);
                }
            });

            // 选择框变化事件
            this.addEventListenerManaged(this.elements.customFieldsContainer, 'change', (e) => {
                const target = e.target;
                const mappingId = parseInt(target.getAttribute('data-mapping-id'));

                if (mappingId && target.classList.contains('field-type-select')) {
                    this.updateCustomMapping(mappingId, 'fieldType', target.value);
                }
            });
        }

        // 监听标签页切换事件
        this.subscribe('tab:changed', (data) => {
            if (data.tabId === 'field-mapping') {
                // 切换到字段映射标签页时刷新数据
                setTimeout(() => {
                    this.loadExistingMappings();
                }, 100);
            }
        });

        if (this.config.debugMode) {
            console.log('NotionFieldMapping: 事件绑定完成');
        }
    }

    /**
     * 渲染组件（增强现有HTML结构）
     */
    render() {
        // 在jQuery兼容模式下，不渲染自己的HTML
        // 而是增强现有的HTML结构
        if (!this.elements.fieldMappingTab) {
            console.warn('NotionFieldMapping: 未找到现有的字段映射表单结构');
            return;
        }

        // 添加组件标识
        this.elements.fieldMappingTab.setAttribute('data-enhanced-by', 'notion-field-mapping');

        // 重新渲染自定义映射（如果需要）
        if (this.state.customMappings.length > 0) {
            this.renderAllCustomMappings();
        }

        if (this.config.debugMode) {
            console.log('NotionFieldMapping: HTML结构增强完成');
        }
    }

    /**
     * 验证字段映射配置
     */
    validateMappings() {
        const errors = [];
        const warnings = [];

        // 验证基础字段映射
        const requiredFields = ['title', 'status'];
        requiredFields.forEach(field => {
            if (!this.state.basicMappings[field]?.trim()) {
                errors.push(`${field === 'title' ? '文章标题' : '状态'}字段映射不能为空`);
            }
        });

        // 验证自定义字段映射
        this.state.customMappings.forEach((mapping, index) => {
            if (mapping.notionProperty?.trim() && !mapping.wpField?.trim()) {
                warnings.push(`第${index + 1}个自定义映射缺少WordPress字段名称`);
            } else if (!mapping.notionProperty?.trim() && mapping.wpField?.trim()) {
                warnings.push(`第${index + 1}个自定义映射缺少Notion属性名称`);
            }
        });

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 获取当前映射配置
     */
    getCurrentMappings() {
        return {
            basic: { ...this.state.basicMappings },
            custom: this.state.customMappings.map(mapping => ({ ...mapping })),
            hasChanges: this.state.hasChanges
        };
    }

    /**
     * 重置映射配置
     */
    resetMappings() {
        this.loadExistingMappings();
        this.setState({ hasChanges: false });
        this.renderAllCustomMappings();

        // 广播重置事件
        this.broadcast('field-mapping:reset', {
            timestamp: Date.now()
        });

        if (this.config.debugMode) {
            console.log('NotionFieldMapping: 映射配置已重置');
        }
    }

    /**
     * 应用映射模板
     */
    applyTemplate(templateName) {
        let templateMappings = {};

        switch (templateName) {
            case 'english':
                templateMappings = {
                    title: 'Title,Name',
                    status: 'Status,Published',
                    post_type: 'Type,Post Type',
                    date: 'Date,Created,Published Date',
                    excerpt: 'Excerpt,Summary,Description',
                    featured_image: 'Featured Image,Cover,Image',
                    categories: 'Category,Categories',
                    tags: 'Tags,Tag',
                    password: 'Password'
                };
                break;
            case 'chinese':
                templateMappings = {
                    title: '标题,名称,Title',
                    status: '状态,发布状态,Status',
                    post_type: '类型,文章类型,Type',
                    date: '日期,发布日期,创建日期,Date',
                    excerpt: '摘要,简介,描述,Excerpt',
                    featured_image: '特色图片,封面,图片,Cover',
                    categories: '分类,类别,Category',
                    tags: '标签,Tags',
                    password: '密码,Password'
                };
                break;
            case 'mixed':
                templateMappings = {
                    title: 'Title,标题,Name,名称',
                    status: 'Status,状态,Published,发布状态',
                    post_type: 'Type,类型,Post Type,文章类型',
                    date: 'Date,日期,Created,创建日期,Published Date,发布日期',
                    excerpt: 'Excerpt,摘要,Summary,简介,Description,描述',
                    featured_image: 'Featured Image,特色图片,Cover,封面,Image,图片',
                    categories: 'Category,分类,Categories,类别',
                    tags: 'Tags,标签,Tag',
                    password: 'Password,密码'
                };
                break;
            default:
                console.warn('NotionFieldMapping: 未知的模板名称', templateName);
                return;
        }

        // 应用模板到基础映射
        this.setState({
            basicMappings: { ...this.state.basicMappings, ...templateMappings },
            hasChanges: true
        });

        // 更新DOM中的输入值
        Object.entries(templateMappings).forEach(([field, value]) => {
            const element = this.elements[field + 'Input'];
            if (element) {
                element.value = value;
            }
        });

        // 广播模板应用事件
        this.broadcast('field-mapping:template-applied', {
            templateName,
            mappings: templateMappings
        });

        if (this.config.debugMode) {
            console.log('NotionFieldMapping: 应用模板', templateName, templateMappings);
        }
    }

    /**
     * HTML转义工具方法
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 组件销毁时的清理
     */
    onBeforeUnmount() {
        if (this.config.debugMode) {
            console.log('NotionFieldMapping: 组件清理完成');
        }
    }
}

// ==================== 组件注册和导出 ====================

// 注册Web Component
if (!customElements.get('notion-field-mapping')) {
    customElements.define('notion-field-mapping', NotionFieldMapping);
}

// 注册到Notion组件注册表
if (window.NotionRegistry) {
    window.NotionRegistry.register('notion-field-mapping', NotionFieldMapping);
} else {
    console.warn('NotionFieldMapping: NotionRegistry未找到，使用标准Web Components注册');
}

// 导出到全局作用域
window.NotionFieldMapping = NotionFieldMapping;

// 创建全局实例（单例模式）
if (!window.notionFieldMappingInstance) {
    // 延迟创建实例，确保DOM已加载
    document.addEventListener('DOMContentLoaded', () => {
        // 查找字段映射容器
        const fieldMappingContainer = document.getElementById('field-mapping');

        if (fieldMappingContainer) {
            // 创建组件实例
            const fieldMappingComponent = document.createElement('notion-field-mapping');
            fieldMappingComponent.style.display = 'none'; // 隐藏，因为我们只是用来增强现有结构

            // 插入到容器中
            fieldMappingContainer.appendChild(fieldMappingComponent);

            // 保存全局实例引用
            window.notionFieldMappingInstance = fieldMappingComponent;

            console.log('NotionFieldMapping: 全局实例已创建并可用');
        } else {
            console.warn('NotionFieldMapping: 未找到字段映射容器');
        }
    });
}

// 提供便捷的全局API
window.NotionFieldMappingAPI = {
    /**
     * 获取字段映射组件实例
     */
    getInstance() {
        return window.notionFieldMappingInstance;
    },

    /**
     * 获取当前映射配置
     */
    getCurrentMappings() {
        const instance = this.getInstance();
        return instance ? instance.getCurrentMappings() : null;
    },

    /**
     * 验证映射配置
     */
    validateMappings() {
        const instance = this.getInstance();
        return instance ? instance.validateMappings() : { isValid: false, errors: ['组件未初始化'], warnings: [] };
    },

    /**
     * 添加自定义映射
     */
    addCustomMapping() {
        const instance = this.getInstance();
        return instance ? instance.addCustomMapping() : false;
    },

    /**
     * 删除自定义映射
     */
    removeCustomMapping(id) {
        const instance = this.getInstance();
        return instance ? instance.removeCustomMapping(id) : false;
    },

    /**
     * 应用映射模板
     */
    applyTemplate(templateName) {
        const instance = this.getInstance();
        return instance ? instance.applyTemplate(templateName) : false;
    },

    /**
     * 重置映射配置
     */
    resetMappings() {
        const instance = this.getInstance();
        return instance ? instance.resetMappings() : false;
    },

    /**
     * 获取基础字段映射
     */
    getBasicMappings() {
        const mappings = this.getCurrentMappings();
        return mappings ? mappings.basic : null;
    },

    /**
     * 获取自定义字段映射
     */
    getCustomMappings() {
        const mappings = this.getCurrentMappings();
        return mappings ? mappings.custom : null;
    },

    /**
     * 检查是否有未保存的更改
     */
    hasUnsavedChanges() {
        const mappings = this.getCurrentMappings();
        return mappings ? mappings.hasChanges : false;
    }
};

console.log('NotionFieldMapping: 组件加载完成，支持基础和自定义字段映射管理');
