/**
 * Notion to WordPress - 关于作者组件
 * 展示作者信息、联系方式、项目链接和版本信息，保持简洁的展示效果
 */

class NotionAboutAuthor extends NotionBaseComponent {
    constructor() {
        super();
        this.state = {
            // 作者信息
            authorInfo: {
                name: '<PERSON><PERSON><PERSON><PERSON>',
                title: '科技爱好者 & AI玩家',
                description: '对互联网、计算机等科技行业充满热情，擅长 AI 工具的使用与调教。此插件在强大的 AI 编程助手 Cursor 和 Augment 的协助下完成，现在将这个有趣的项目分享给大家。',
                avatar: '',
                links: [
                    {
                        id: 'website',
                        name: '个人网站',
                        url: 'https://frankloong.com',
                        icon: '🌐',
                        description: '访问作者的个人网站'
                    },
                    {
                        id: 'email',
                        name: '联系邮箱',
                        url: 'mailto:<EMAIL>',
                        icon: '📧',
                        description: '发送邮件联系作者'
                    },
                    {
                        id: 'github',
                        name: '<PERSON><PERSON><PERSON><PERSON>',
                        url: 'https://github.com/<PERSON>-<PERSON><PERSON>/Notion-to-WordPress',
                        icon: '💻',
                        description: '查看项目源代码'
                    }
                ]
            },
            // 插件信息
            pluginInfo: {
                version: '',
                license: 'GPL v3',
                compatibility: 'WordPress 5.0+',
                lastUpdate: '',
                downloadCount: 0
            },
            // 致谢信息
            acknowledgments: [
                {
                    name: 'NotionNext',
                    url: 'https://github.com/tangly1024/NotionNext',
                    description: '基于 Notion 的强大静态博客系统'
                },
                {
                    name: 'Elog',
                    url: 'https://github.com/LetTTGACO/elog',
                    description: '支持多平台的开源博客写作客户端'
                },
                {
                    name: 'notion-content',
                    url: 'https://github.com/pchang78/notion-content',
                    description: 'Notion 内容管理解决方案'
                }
            ],
            // 状态管理
            isLoading: false,
            error: null,
            lastRefresh: null
        };
        
        // 缓存DOM元素引用
        this.elements = {};
    }

    init() {
        // 缓存DOM元素
        this.cacheElements();
        
        // 加载作者信息
        this.loadAuthorInfo();
        
        // 加载插件信息
        this.loadPluginInfo();
        
        // 绑定事件监听器
        this.bindEvents();
        
        // 渲染组件增强
        this.render();
        
        if (this.config.debugMode) {
            console.log('NotionAboutAuthor: 组件初始化完成', this.state);
        }
    }

    /**
     * 缓存DOM元素引用
     */
    cacheElements() {
        this.elements = {
            // 关于作者标签页容器
            aboutTab: document.getElementById('about-author'),
            
            // 现有的作者信息区域
            authorInfo: document.querySelector('#about-author .author-info'),
            pluginInfo: document.querySelector('#about-author .plugin-info'),
            acknowledgments: document.querySelector('#about-author .acknowledgments'),
            
            // 设置区域
            settingsSection: document.querySelector('#about-author .notion-wp-settings-section')
        };
    }

    /**
     * 加载作者信息
     */
    loadAuthorInfo() {
        try {
            // 从现有DOM中提取头像路径
            const avatarImg = this.elements.authorInfo?.querySelector('img');
            if (avatarImg) {
                this.setState({
                    authorInfo: {
                        ...this.state.authorInfo,
                        avatar: avatarImg.src
                    }
                });
            }
            
            if (this.config.debugMode) {
                console.log('NotionAboutAuthor: 已加载作者信息', this.state.authorInfo);
            }
        } catch (error) {
            this.handleError('加载作者信息失败', error);
        }
    }

    /**
     * 加载插件信息
     */
    loadPluginInfo() {
        try {
            // 从现有DOM中提取版本信息
            const versionElement = this.elements.pluginInfo?.querySelector('.info-value');
            if (versionElement) {
                this.setState({
                    pluginInfo: {
                        ...this.state.pluginInfo,
                        version: versionElement.textContent.trim()
                    }
                });
            }
            
            // 设置最后更新时间
            this.setState({
                pluginInfo: {
                    ...this.state.pluginInfo,
                    lastUpdate: new Date().toLocaleDateString('zh-CN')
                }
            });
            
            if (this.config.debugMode) {
                console.log('NotionAboutAuthor: 已加载插件信息', this.state.pluginInfo);
            }
        } catch (error) {
            this.handleError('加载插件信息失败', error);
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 标签页切换监听
        this.bindTabSwitchListener();
        
        // 链接点击统计
        this.bindLinkClickEvents();
        
        // 复制功能
        this.bindCopyEvents();
    }

    /**
     * 绑定标签页切换监听
     */
    bindTabSwitchListener() {
        // 监听标签页切换事件
        this.subscribe('tab:switched', (data) => {
            if (data.tabId === 'about-author') {
                // 当切换到关于作者标签页时，刷新信息
                this.refreshInfo();
            }
        });
    }

    /**
     * 绑定链接点击事件
     */
    bindLinkClickEvents() {
        if (this.elements.aboutTab) {
            this.addEventListenerManaged(this.elements.aboutTab, 'click', (e) => {
                const link = e.target.closest('.author-link, .reference-item a');
                if (link) {
                    this.trackLinkClick(link);
                }
            });
        }
    }

    /**
     * 绑定复制事件
     */
    bindCopyEvents() {
        if (this.elements.aboutTab) {
            this.addEventListenerManaged(this.elements.aboutTab, 'click', (e) => {
                const copyBtn = e.target.closest('.copy-btn');
                if (copyBtn) {
                    const textToCopy = copyBtn.getAttribute('data-copy');
                    if (textToCopy) {
                        this.copyToClipboard(textToCopy);
                    }
                }
            });
        }
    }

    /**
     * 跟踪链接点击
     */
    trackLinkClick(link) {
        const url = link.href;
        const text = link.textContent.trim();
        
        if (this.config.debugMode) {
            console.log('NotionAboutAuthor: 链接点击', { url, text });
        }
        
        // 可以在这里添加分析代码
        this.publish('link:clicked', { url, text, timestamp: Date.now() });
    }

    /**
     * 复制到剪贴板
     */
    async copyToClipboard(text) {
        try {
            if (navigator.clipboard && window.isSecureContext) {
                await navigator.clipboard.writeText(text);
            } else {
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                textArea.style.position = 'fixed';
                textArea.style.left = '-999999px';
                textArea.style.top = '-999999px';
                document.body.appendChild(textArea);
                textArea.focus();
                textArea.select();
                document.execCommand('copy');
                textArea.remove();
            }
            
            this.showSuccess('已复制到剪贴板');
        } catch (error) {
            this.handleError('复制失败', error);
        }
    }

    /**
     * 刷新信息
     */
    refreshInfo() {
        this.setState({ 
            lastRefresh: new Date().toLocaleString(),
            pluginInfo: {
                ...this.state.pluginInfo,
                lastUpdate: new Date().toLocaleDateString('zh-CN')
            }
        });
        
        if (this.config.debugMode) {
            console.log('NotionAboutAuthor: 信息已刷新');
        }
    }

    /**
     * 渲染组件增强
     */
    render() {
        // 如果组件容器不存在，创建增强显示
        if (!this.querySelector('.about-enhanced-container')) {
            this.createEnhancedAboutInterface();
        }
        
        // 更新作者卡片
        this.updateAuthorCard();
        
        // 更新插件信息
        this.updatePluginInfo();
        
        // 更新致谢信息
        this.updateAcknowledgments();
    }

    /**
     * 创建增强的关于界面
     */
    createEnhancedAboutInterface() {
        const settingsSection = this.elements.settingsSection;
        if (!settingsSection) return;

        // 创建增强容器
        const enhancedContainer = document.createElement('div');
        enhancedContainer.className = 'about-enhanced-container';
        
        // 创建作者卡片增强
        const authorCard = this.createEnhancedAuthorCard();
        enhancedContainer.appendChild(authorCard);
        
        // 创建插件信息增强
        const pluginCard = this.createEnhancedPluginInfo();
        enhancedContainer.appendChild(pluginCard);
        
        // 创建致谢信息增强
        const acknowledgementsCard = this.createEnhancedAcknowledgments();
        enhancedContainer.appendChild(acknowledgementsCard);
        
        // 插入到设置区域
        settingsSection.appendChild(enhancedContainer);
        
        if (this.config.debugMode) {
            console.log('NotionAboutAuthor: 已创建增强界面');
        }
    }

    /**
     * 创建增强的作者卡片
     */
    createEnhancedAuthorCard() {
        const card = document.createElement('div');
        card.className = 'author-card-enhanced';
        
        const { authorInfo } = this.state;
        
        card.innerHTML = `
            <div class="author-header">
                <div class="author-avatar-enhanced">
                    <img src="${authorInfo.avatar}" alt="${authorInfo.name}" onerror="this.style.display='none'">
                    <div class="avatar-badge">👨‍💻</div>
                </div>
                <div class="author-basic-info">
                    <h3 class="author-name">${authorInfo.name}</h3>
                    <p class="author-title">${authorInfo.title}</p>
                    <div class="author-stats">
                        <span class="stat-item">
                            <span class="stat-icon">🚀</span>
                            <span class="stat-text">插件开发者</span>
                        </span>
                        <span class="stat-item">
                            <span class="stat-icon">⭐</span>
                            <span class="stat-text">开源贡献者</span>
                        </span>
                    </div>
                </div>
            </div>
            
            <div class="author-description">
                <p>${authorInfo.description}</p>
            </div>
            
            <div class="author-links-enhanced">
                ${authorInfo.links.map(link => `
                    <a href="${link.url}" target="_blank" class="author-link-enhanced" title="${link.description}">
                        <span class="link-icon">${link.icon}</span>
                        <span class="link-text">${link.name}</span>
                        <span class="link-arrow">→</span>
                    </a>
                `).join('')}
            </div>
            
            <div class="author-actions">
                <button type="button" class="copy-btn" data-copy="${authorInfo.links.find(l => l.id === 'email')?.url || ''}">
                    <span class="dashicons dashicons-admin-page"></span>
                    复制邮箱
                </button>
                <button type="button" class="copy-btn" data-copy="${authorInfo.links.find(l => l.id === 'github')?.url || ''}">
                    <span class="dashicons dashicons-admin-page"></span>
                    复制项目链接
                </button>
            </div>
        `;
        
        return card;
    }

    /**
     * 创建增强的插件信息
     */
    createEnhancedPluginInfo() {
        const card = document.createElement('div');
        card.className = 'plugin-info-enhanced';

        const { pluginInfo } = this.state;

        card.innerHTML = `
            <div class="plugin-header">
                <h4>📦 插件信息</h4>
                <span class="plugin-status">
                    <span class="status-dot active"></span>
                    活跃开发中
                </span>
            </div>

            <div class="plugin-details">
                <div class="info-grid-enhanced">
                    <div class="info-item-enhanced">
                        <div class="info-icon">🏷️</div>
                        <div class="info-content">
                            <span class="info-label">版本</span>
                            <span class="info-value">${pluginInfo.version}</span>
                        </div>
                    </div>

                    <div class="info-item-enhanced">
                        <div class="info-icon">📄</div>
                        <div class="info-content">
                            <span class="info-label">许可证</span>
                            <span class="info-value">${pluginInfo.license}</span>
                        </div>
                    </div>

                    <div class="info-item-enhanced">
                        <div class="info-icon">🔧</div>
                        <div class="info-content">
                            <span class="info-label">兼容性</span>
                            <span class="info-value">${pluginInfo.compatibility}</span>
                        </div>
                    </div>

                    <div class="info-item-enhanced">
                        <div class="info-icon">📅</div>
                        <div class="info-content">
                            <span class="info-label">最后更新</span>
                            <span class="info-value">${pluginInfo.lastUpdate}</span>
                        </div>
                    </div>
                </div>

                <div class="plugin-features">
                    <h5>✨ 主要特性</h5>
                    <div class="features-list">
                        <span class="feature-tag">🔄 自动同步</span>
                        <span class="feature-tag">🎨 格式保留</span>
                        <span class="feature-tag">📊 性能监控</span>
                        <span class="feature-tag">🔧 调试工具</span>
                        <span class="feature-tag">📱 响应式</span>
                        <span class="feature-tag">🌐 多语言</span>
                    </div>
                </div>
            </div>
        `;

        return card;
    }

    /**
     * 创建增强的致谢信息
     */
    createEnhancedAcknowledgments() {
        const card = document.createElement('div');
        card.className = 'acknowledgments-enhanced';

        const { acknowledgments } = this.state;

        card.innerHTML = `
            <div class="acknowledgments-header">
                <h4>🙏 致谢与参考</h4>
                <p class="acknowledgments-intro">本项目的开发过程中参考了以下优秀的开源项目：</p>
            </div>

            <div class="reference-projects-enhanced">
                ${acknowledgments.map(project => `
                    <div class="reference-item-enhanced">
                        <div class="reference-icon">🔗</div>
                        <div class="reference-content">
                            <h6 class="reference-name">
                                <a href="${project.url}" target="_blank">${project.name}</a>
                            </h6>
                            <p class="reference-description">${project.description}</p>
                        </div>
                        <div class="reference-action">
                            <a href="${project.url}" target="_blank" class="reference-link">
                                <span class="dashicons dashicons-external"></span>
                            </a>
                        </div>
                    </div>
                `).join('')}
            </div>

            <div class="acknowledgments-footer">
                <p><em>💝 感谢这些项目及其维护者对开源社区的贡献！</em></p>
                <div class="community-links">
                    <a href="https://github.com/Frank-Loong/Notion-to-WordPress/issues" target="_blank" class="community-link">
                        <span class="dashicons dashicons-sos"></span>
                        报告问题
                    </a>
                    <a href="https://github.com/Frank-Loong/Notion-to-WordPress/discussions" target="_blank" class="community-link">
                        <span class="dashicons dashicons-format-chat"></span>
                        参与讨论
                    </a>
                    <a href="https://github.com/Frank-Loong/Notion-to-WordPress/fork" target="_blank" class="community-link">
                        <span class="dashicons dashicons-share"></span>
                        贡献代码
                    </a>
                </div>
            </div>
        `;

        return card;
    }

    /**
     * 更新作者卡片
     */
    updateAuthorCard() {
        const authorCard = this.querySelector('.author-card-enhanced');
        if (!authorCard) return;

        // 更新头像
        const avatar = authorCard.querySelector('.author-avatar-enhanced img');
        if (avatar && this.state.authorInfo.avatar) {
            avatar.src = this.state.authorInfo.avatar;
        }

        // 更新链接
        const links = authorCard.querySelectorAll('.author-link-enhanced');
        links.forEach((link, index) => {
            const linkInfo = this.state.authorInfo.links[index];
            if (linkInfo) {
                link.href = linkInfo.url;
                link.title = linkInfo.description;
            }
        });
    }

    /**
     * 更新插件信息
     */
    updatePluginInfo() {
        const pluginCard = this.querySelector('.plugin-info-enhanced');
        if (!pluginCard) return;

        // 更新版本信息
        const versionValue = pluginCard.querySelector('.info-item-enhanced:first-child .info-value');
        if (versionValue) {
            versionValue.textContent = this.state.pluginInfo.version;
        }

        // 更新最后更新时间
        const updateValue = pluginCard.querySelector('.info-item-enhanced:last-child .info-value');
        if (updateValue) {
            updateValue.textContent = this.state.pluginInfo.lastUpdate;
        }
    }

    /**
     * 更新致谢信息
     */
    updateAcknowledgments() {
        const acknowledgementsCard = this.querySelector('.acknowledgments-enhanced');
        if (!acknowledgementsCard) return;

        // 更新项目链接
        const projectLinks = acknowledgementsCard.querySelectorAll('.reference-item-enhanced a');
        projectLinks.forEach((link, index) => {
            const project = this.state.acknowledgments[Math.floor(index / 2)];
            if (project) {
                link.href = project.url;
            }
        });
    }

    /**
     * 检查组件是否可见
     */
    isVisible() {
        const aboutTab = this.elements.aboutTab;
        if (!aboutTab) return false;

        // 检查标签页是否激活
        return aboutTab.classList.contains('active') ||
               aboutTab.style.display !== 'none';
    }

    /**
     * 获取作者统计信息
     */
    getAuthorStats() {
        return {
            name: this.state.authorInfo.name,
            linksCount: this.state.authorInfo.links.length,
            pluginVersion: this.state.pluginInfo.version,
            acknowledgementsCount: this.state.acknowledgments.length,
            lastRefresh: this.state.lastRefresh
        };
    }

    /**
     * 导出作者信息
     */
    exportAuthorInfo() {
        const info = {
            author: this.state.authorInfo,
            plugin: this.state.pluginInfo,
            acknowledgments: this.state.acknowledgments,
            exportTime: new Date().toISOString()
        };

        const content = JSON.stringify(info, null, 2);
        const blob = new Blob([content], { type: 'application/json' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `notion-wp-author-info-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        URL.revokeObjectURL(url);
        this.showSuccess('作者信息已导出');
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        this.setState({ error: null });

        // 创建临时成功提示
        const successDiv = document.createElement('div');
        successDiv.className = 'about-success notice notice-success is-dismissible';
        successDiv.innerHTML = `
            <p><strong>成功:</strong> ${this.escapeHtml(message)}</p>
            <button type="button" class="notice-dismiss">
                <span class="screen-reader-text">忽略此通知</span>
            </button>
        `;

        // 插入到关于作者区域顶部
        const settingsSection = this.elements.settingsSection;
        if (settingsSection) {
            settingsSection.insertBefore(successDiv, settingsSection.firstChild);
        }

        // 3秒后自动移除
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.remove();
            }
        }, 3000);
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        this.setState({ error: message });

        // 创建错误提示
        const errorDiv = document.createElement('div');
        errorDiv.className = 'about-error notice notice-error is-dismissible';
        errorDiv.innerHTML = `
            <p><strong>错误:</strong> ${this.escapeHtml(message)}</p>
            <button type="button" class="notice-dismiss">
                <span class="screen-reader-text">忽略此通知</span>
            </button>
        `;

        // 插入到关于作者区域顶部
        const settingsSection = this.elements.settingsSection;
        if (settingsSection) {
            settingsSection.insertBefore(errorDiv, settingsSection.firstChild);
        }
    }

    /**
     * 处理错误
     */
    handleError(message, error) {
        console.error(message, error);
        this.showError(`${message}: ${error.message || error}`);
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        if (typeof text !== 'string') return text;

        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 重置关于界面
     */
    reset() {
        // 重置状态
        this.setState({
            error: null,
            lastRefresh: null
        });

        // 重新加载信息
        this.loadAuthorInfo();
        this.loadPluginInfo();

        // 重新渲染
        this.render();

        if (this.config.debugMode) {
            console.log('NotionAboutAuthor: 界面已重置');
        }
    }

    cleanup() {
        // 清理DOM元素引用
        this.elements = {};

        // 调用父类清理方法
        super.cleanup();

        if (this.config.debugMode) {
            console.log('NotionAboutAuthor: 组件已清理');
        }
    }
}

// 注册组件
if (window.NotionRegistry) {
    window.NotionRegistry.register('notion-about-author', NotionAboutAuthor);
} else {
    console.error('NotionRegistry not found. Make sure component-registry.js is loaded first.');
}
