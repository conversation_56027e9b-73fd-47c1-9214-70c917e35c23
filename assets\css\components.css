/**
 * Notion to WordPress - Web Components 样式
 * 现代化组件样式系统
 */

/* =============== 扩展CSS设计系统变量 =============== */
:root {
    /* 主色调 */
    --notion-primary: #2563eb;
    --notion-primary-dark: #1d4ed8;
    --notion-primary-light: #3b82f6;
    --notion-primary-lighter: #dbeafe;
    --notion-primary-lightest: #eff6ff;

    /* 状态颜色 */
    --notion-success: #10b981;
    --notion-success-light: #d1fae5;
    --notion-success-dark: #047857;
    --notion-warning: #f59e0b;
    --notion-warning-light: #fef3c7;
    --notion-warning-dark: #d97706;
    --notion-error: #ef4444;
    --notion-error-light: #fee2e2;
    --notion-error-dark: #dc2626;
    --notion-info: #06b6d4;
    --notion-info-light: #cffafe;
    --notion-info-dark: #0891b2;

    /* 灰度色彩 */
    --notion-gray-50: #f9fafb;
    --notion-gray-100: #f3f4f6;
    --notion-gray-200: #e5e7eb;
    --notion-gray-300: #d1d5db;
    --notion-gray-400: #9ca3af;
    --notion-gray-500: #6b7280;
    --notion-gray-600: #4b5563;
    --notion-gray-700: #374151;
    --notion-gray-800: #1f2937;
    --notion-gray-900: #111827;

    /* 布局尺寸 */
    --notion-border-radius: 8px;
    --notion-border-radius-sm: 4px;
    --notion-border-radius-lg: 12px;
    --notion-border-radius-xl: 16px;
    --notion-border-radius-full: 9999px;

    /* 阴影系统 */
    --notion-shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --notion-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --notion-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --notion-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --notion-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --notion-shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

    /* 动画系统 */
    --notion-transition: all 0.2s ease-in-out;
    --notion-transition-fast: all 0.1s ease-in-out;
    --notion-transition-slow: all 0.3s ease-in-out;
    --notion-transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);

    /* 间距系统 */
    --notion-space-1: 0.25rem;  /* 4px */
    --notion-space-2: 0.5rem;   /* 8px */
    --notion-space-3: 0.75rem;  /* 12px */
    --notion-space-4: 1rem;     /* 16px */
    --notion-space-5: 1.25rem;  /* 20px */
    --notion-space-6: 1.5rem;   /* 24px */
    --notion-space-8: 2rem;     /* 32px */
    --notion-space-10: 2.5rem;  /* 40px */
    --notion-space-12: 3rem;    /* 48px */
    --notion-space-16: 4rem;    /* 64px */
    --notion-space-20: 5rem;    /* 80px */

    /* 字体系统 */
    --notion-font-family: -apple-system, BlinkMacSystemFont, "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
    --notion-font-mono: "SF Mono", Monaco, "Cascadia Code", "Roboto Mono", Consolas, "Courier New", monospace;
    --notion-font-size-xs: 0.75rem;    /* 12px */
    --notion-font-size-sm: 0.875rem;   /* 14px */
    --notion-font-size-base: 1rem;     /* 16px */
    --notion-font-size-lg: 1.125rem;   /* 18px */
    --notion-font-size-xl: 1.25rem;    /* 20px */
    --notion-font-size-2xl: 1.5rem;    /* 24px */
    --notion-font-size-3xl: 1.875rem;  /* 30px */
    --notion-font-weight-normal: 400;
    --notion-font-weight-medium: 500;
    --notion-font-weight-semibold: 600;
    --notion-font-weight-bold: 700;
    --notion-line-height-tight: 1.25;
    --notion-line-height-normal: 1.5;
    --notion-line-height-relaxed: 1.75;

    /* 组件尺寸 */
    --notion-component-padding: var(--notion-space-5);
    --notion-component-margin: var(--notion-space-4);
    --notion-input-height: 2.5rem;     /* 40px */
    --notion-input-height-sm: 2rem;    /* 32px */
    --notion-input-height-lg: 3rem;    /* 48px */
    --notion-button-height: 2.25rem;   /* 36px */
    --notion-button-height-sm: 2rem;   /* 32px */
    --notion-button-height-lg: 2.75rem; /* 44px */

    /* 响应式断点 */
    --notion-breakpoint-sm: 640px;
    --notion-breakpoint-md: 768px;
    --notion-breakpoint-lg: 1024px;
    --notion-breakpoint-xl: 1280px;
    --notion-breakpoint-2xl: 1536px;

    /* Z-index 层级 */
    --notion-z-dropdown: 1000;
    --notion-z-sticky: 1020;
    --notion-z-fixed: 1030;
    --notion-z-modal-backdrop: 1040;
    --notion-z-modal: 1050;
    --notion-z-popover: 1060;
    --notion-z-tooltip: 1070;
    --notion-z-toast: 1080;
}

/* =============== 统一组件基础样式系统 =============== */

/* 基础组件容器 */
.notion-component {
    background: white;
    border-radius: var(--notion-border-radius);
    box-shadow: var(--notion-shadow);
    padding: var(--notion-component-padding);
    margin-bottom: var(--notion-component-margin);
    font-family: var(--notion-font-family);
    font-size: var(--notion-font-size-sm);
    line-height: var(--notion-line-height-normal);
    color: var(--notion-gray-900);
    transition: var(--notion-transition);
}

.notion-component:hover {
    box-shadow: var(--notion-shadow-md);
}

/* 组件标题样式 */
.notion-component-title {
    font-size: var(--notion-font-size-lg);
    font-weight: var(--notion-font-weight-semibold);
    color: var(--notion-gray-900);
    margin: 0 0 var(--notion-space-4) 0;
    line-height: var(--notion-line-height-tight);
}

.notion-component-subtitle {
    font-size: var(--notion-font-size-sm);
    color: var(--notion-gray-600);
    margin: 0 0 var(--notion-space-6) 0;
    line-height: var(--notion-line-height-normal);
}

/* 统一表单元素样式 */
.notion-input {
    width: 100%;
    height: var(--notion-input-height);
    padding: 0 var(--notion-space-3);
    border: 1px solid var(--notion-gray-300);
    border-radius: var(--notion-border-radius);
    font-size: var(--notion-font-size-sm);
    font-family: var(--notion-font-family);
    color: var(--notion-gray-900);
    background: white;
    transition: var(--notion-transition);
    outline: none;
}

.notion-input:focus {
    border-color: var(--notion-primary);
    box-shadow: 0 0 0 3px var(--notion-primary-lighter);
}

.notion-input:disabled {
    background: var(--notion-gray-50);
    color: var(--notion-gray-500);
    cursor: not-allowed;
}

.notion-input.error {
    border-color: var(--notion-error);
    box-shadow: 0 0 0 3px var(--notion-error-light);
}

.notion-input-sm {
    height: var(--notion-input-height-sm);
    font-size: var(--notion-font-size-xs);
}

.notion-input-lg {
    height: var(--notion-input-height-lg);
    font-size: var(--notion-font-size-base);
}

/* 统一按钮样式系统 */
.notion-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--notion-space-2);
    height: var(--notion-button-height);
    padding: 0 var(--notion-space-4);
    border: 1px solid transparent;
    border-radius: var(--notion-border-radius);
    font-size: var(--notion-font-size-sm);
    font-weight: var(--notion-font-weight-medium);
    font-family: var(--notion-font-family);
    line-height: 1;
    cursor: pointer;
    transition: var(--notion-transition);
    text-decoration: none;
    white-space: nowrap;
    outline: none;
}

.notion-button:focus {
    box-shadow: 0 0 0 3px var(--notion-primary-lighter);
}

.notion-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 按钮变体 */
.notion-button-primary {
    background: var(--notion-primary);
    color: white;
    border-color: var(--notion-primary);
}

.notion-button-primary:hover:not(:disabled) {
    background: var(--notion-primary-dark);
    border-color: var(--notion-primary-dark);
}

.notion-button-secondary {
    background: white;
    color: var(--notion-gray-700);
    border-color: var(--notion-gray-300);
}

.notion-button-secondary:hover:not(:disabled) {
    background: var(--notion-gray-50);
    border-color: var(--notion-gray-400);
}

.notion-button-success {
    background: var(--notion-success);
    color: white;
    border-color: var(--notion-success);
}

.notion-button-success:hover:not(:disabled) {
    background: var(--notion-success-dark);
    border-color: var(--notion-success-dark);
}

.notion-button-warning {
    background: var(--notion-warning);
    color: white;
    border-color: var(--notion-warning);
}

.notion-button-warning:hover:not(:disabled) {
    background: var(--notion-warning-dark);
    border-color: var(--notion-warning-dark);
}

.notion-button-error {
    background: var(--notion-error);
    color: white;
    border-color: var(--notion-error);
}

.notion-button-error:hover:not(:disabled) {
    background: var(--notion-error-dark);
    border-color: var(--notion-error-dark);
}

.notion-button-ghost {
    background: transparent;
    color: var(--notion-gray-700);
    border-color: transparent;
}

.notion-button-ghost:hover:not(:disabled) {
    background: var(--notion-gray-100);
    color: var(--notion-gray-900);
}

/* 按钮尺寸 */
.notion-button-sm {
    height: var(--notion-button-height-sm);
    padding: 0 var(--notion-space-3);
    font-size: var(--notion-font-size-xs);
}

.notion-button-lg {
    height: var(--notion-button-height-lg);
    padding: 0 var(--notion-space-6);
    font-size: var(--notion-font-size-base);
}

/* 按钮图标 */
.notion-button-icon {
    font-size: var(--notion-font-size-base);
    line-height: 1;
}

/* =============== 标签页管理器样式 =============== */
.notion-tab-manager {
    width: 100%;
    background: white;
    border-radius: var(--notion-border-radius-lg);
    box-shadow: var(--notion-shadow-lg);
    overflow: hidden;
}

/* 标签页导航 */
.tab-navigation {
    display: flex;
    background: var(--notion-gray-50);
    border-bottom: 1px solid var(--notion-gray-200);
    padding: 0;
    margin: 0;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.tab-navigation::-webkit-scrollbar {
    display: none;
}

.tab-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    background: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--notion-gray-600);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--notion-transition);
    white-space: nowrap;
    min-width: fit-content;
}

.tab-button:hover {
    background: var(--notion-gray-100);
    color: var(--notion-gray-800);
}

.tab-button.active {
    background: white;
    color: var(--notion-primary);
    border-bottom-color: var(--notion-primary);
}

.tab-icon {
    font-size: 16px;
}

.tab-label {
    font-weight: 500;
}

/* 标签页内容容器 */
.tab-content-container {
    position: relative;
    min-height: 500px;
}

.tab-content {
    display: none;
    padding: 24px;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 占位符样式 */
.tab-placeholder,
.settings-placeholder {
    padding: 40px;
    text-align: center;
    color: var(--notion-gray-500);
    background: var(--notion-gray-50);
    border-radius: var(--notion-border-radius);
    border: 2px dashed var(--notion-gray-200);
}

/* =============== 性能监控组件样式 =============== */
.notion-performance-monitor {
    width: 100%;
}

.performance-header {
    margin-bottom: 24px;
}

.performance-header h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--notion-gray-900);
}

.performance-header p {
    margin: 0 0 16px 0;
    color: var(--notion-gray-600);
    font-size: 14px;
    line-height: 1.5;
}

.performance-actions {
    display: flex;
    gap: 12px;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: var(--notion-border-radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--notion-transition);
    text-decoration: none;
}

.btn-primary {
    background: var(--notion-primary);
    color: white;
    border-color: var(--notion-primary);
}

.btn-primary:hover {
    background: var(--notion-primary-dark);
    border-color: var(--notion-primary-dark);
}

.btn-secondary {
    background: white;
    color: var(--notion-gray-700);
    border-color: var(--notion-gray-300);
}

.btn-secondary:hover {
    background: var(--notion-gray-50);
    border-color: var(--notion-gray-400);
}

/* 状态卡片 */
.status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.status-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: white;
    border: 1px solid var(--notion-gray-200);
    border-radius: var(--notion-border-radius-lg);
    box-shadow: var(--notion-shadow);
    transition: var(--notion-transition);
}

.status-card:hover {
    box-shadow: var(--notion-shadow-lg);
    transform: translateY(-2px);
}

.status-card.idle {
    border-left: 4px solid var(--notion-gray-400);
}

.status-card.running,
.status-card.processing {
    border-left: 4px solid var(--notion-info);
}

.status-card.success {
    border-left: 4px solid var(--notion-success);
}

.status-card.error {
    border-left: 4px solid var(--notion-error);
}

.card-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--notion-gray-100);
    border-radius: var(--notion-border-radius);
}

.card-content h3 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 500;
    color: var(--notion-gray-600);
}

.status-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--notion-gray-900);
    margin-bottom: 4px;
}

.status-detail {
    font-size: 12px;
    color: var(--notion-gray-500);
}

/* 性能监控部分 */
.performance-section {
    margin-bottom: 32px;
    padding: 24px;
    background: white;
    border: 1px solid var(--notion-gray-200);
    border-radius: var(--notion-border-radius-lg);
}

.performance-section h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--notion-gray-900);
}

/* 异步状态信息 */
.async-status-container {
    display: grid;
    gap: 16px;
}

.status-info {
    display: grid;
    gap: 8px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--notion-gray-100);
}

.info-row:last-child {
    border-bottom: none;
}

.info-row .label {
    font-weight: 500;
    color: var(--notion-gray-700);
}

.info-row .value {
    color: var(--notion-gray-900);
}

.value.status-idle {
    color: var(--notion-gray-500);
}

.value.status-running {
    color: var(--notion-info);
}

.value.status-error {
    color: var(--notion-error);
}

.value.status-completed {
    color: var(--notion-success);
}

/* 进度条 */
.progress-container {
    margin-top: 16px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--notion-gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--notion-primary), var(--notion-primary-light));
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    margin-top: 8px;
    font-size: 12px;
    color: var(--notion-gray-600);
}

/* 队列状态统计 */
.queue-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 16px;
}

.stat-item {
    text-align: center;
    padding: 16px;
    background: var(--notion-gray-50);
    border-radius: var(--notion-border-radius);
    border: 1px solid var(--notion-gray-200);
}

.stat-item.processing {
    background: rgba(6, 182, 212, 0.1);
    border-color: var(--notion-info);
}

.stat-item.success {
    background: rgba(16, 185, 129, 0.1);
    border-color: var(--notion-success);
}

.stat-item.error {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--notion-error);
}

.stat-item.warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: var(--notion-warning);
}

.stat-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--notion-gray-900);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--notion-gray-600);
    font-weight: 500;
}

/* 加载和错误状态 */
.performance-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: var(--notion-gray-600);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--notion-gray-200);
    border-top: 3px solid var(--notion-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
}

.performance-error {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--notion-error);
    border-radius: var(--notion-border-radius);
    color: var(--notion-error);
    margin-top: 16px;
}

.error-icon {
    font-size: 20px;
}

.error-message {
    flex: 1;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tab-navigation {
        flex-wrap: wrap;
    }
    
    .tab-button {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }
    
    .status-cards {
        grid-template-columns: 1fr;
    }
    
    .queue-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .tab-content {
        padding: 16px;
    }
}

/* =============== 扩展响应式设计系统 =============== */

/* 移动设备优先的响应式布局 */
@media (max-width: 640px) {
    /* 组件间距调整 */
    .notion-component {
        padding: var(--notion-space-4);
        margin-bottom: var(--notion-space-3);
    }

    /* 标签页导航响应式 */
    .tab-navigation {
        flex-wrap: wrap;
        gap: var(--notion-space-1);
    }

    .tab-button {
        min-width: auto;
        flex: 1;
        padding: var(--notion-space-3) var(--notion-space-4);
        font-size: var(--notion-font-size-xs);
    }

    /* 按钮响应式 */
    .notion-button {
        width: 100%;
        justify-content: center;
    }

    .notion-button-group {
        flex-direction: column;
        gap: var(--notion-space-2);
    }

    /* 表单元素响应式 */
    .notion-form-row {
        flex-direction: column;
        gap: var(--notion-space-3);
    }

    /* 卡片网格响应式 */
    .status-grid {
        grid-template-columns: 1fr;
        gap: var(--notion-space-3);
    }

    /* 字体大小调整 */
    .notion-component-title {
        font-size: var(--notion-font-size-base);
    }

    /* 性能监控组件响应式 */
    .performance-actions {
        flex-direction: column;
        gap: var(--notion-space-2);
    }

    .queue-stats {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 641px) and (max-width: 768px) {
    /* 平板设备样式 */
    .tab-navigation {
        flex-wrap: nowrap;
        overflow-x: auto;
    }

    .status-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .notion-button-group {
        flex-wrap: wrap;
    }

    .queue-stats {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 769px) and (max-width: 1024px) {
    /* 小桌面设备样式 */
    .status-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .notion-component {
        padding: var(--notion-space-6);
    }

    .queue-stats {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1025px) {
    /* 大桌面设备样式 */
    .status-grid {
        grid-template-columns: repeat(4, 1fr);
    }

    .notion-component {
        padding: var(--notion-space-8);
    }

    .tab-button {
        padding: var(--notion-space-4) var(--notion-space-6);
    }

    .queue-stats {
        grid-template-columns: repeat(4, 1fr);
    }
}

/* =============== 动画效果系统 =============== */

/* 淡入动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 滑入动画 */
@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 缩放动画 */
@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 弹跳动画 */
@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* 脉冲动画 */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

/* 旋转动画 */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 动画类 */
.animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-in {
    animation: slideIn 0.3s ease-out;
}

.animate-scale-in {
    animation: scaleIn 0.2s ease-out;
}

.animate-bounce {
    animation: bounce 1s ease-in-out;
}

.animate-pulse {
    animation: pulse 2s ease-in-out infinite;
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* 悬停动画 */
.hover-lift {
    transition: var(--notion-transition);
}

.hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: var(--notion-shadow-lg);
}

.hover-scale {
    transition: var(--notion-transition);
}

.hover-scale:hover {
    transform: scale(1.02);
}

/* =============== 主题支持系统 =============== */

/* 深色主题变量 */
@media (prefers-color-scheme: dark) {
    :root {
        --notion-gray-50: #1f2937;
        --notion-gray-100: #374151;
        --notion-gray-200: #4b5563;
        --notion-gray-300: #6b7280;
        --notion-gray-400: #9ca3af;
        --notion-gray-500: #d1d5db;
        --notion-gray-600: #e5e7eb;
        --notion-gray-700: #f3f4f6;
        --notion-gray-800: #f9fafb;
        --notion-gray-900: #ffffff;
    }

    .notion-component {
        background: var(--notion-gray-100);
        color: var(--notion-gray-900);
    }

    .notion-input {
        background: var(--notion-gray-50);
        border-color: var(--notion-gray-300);
        color: var(--notion-gray-900);
    }

    .notion-button-secondary {
        background: var(--notion-gray-200);
        color: var(--notion-gray-900);
        border-color: var(--notion-gray-400);
    }
}

/* 手动深色主题类 */
.dark-theme {
    --notion-gray-50: #1f2937;
    --notion-gray-100: #374151;
    --notion-gray-200: #4b5563;
    --notion-gray-300: #6b7280;
    --notion-gray-400: #9ca3af;
    --notion-gray-500: #d1d5db;
    --notion-gray-600: #e5e7eb;
    --notion-gray-700: #f3f4f6;
    --notion-gray-800: #f9fafb;
    --notion-gray-900: #ffffff;
}

.dark-theme .notion-component {
    background: var(--notion-gray-100);
    color: var(--notion-gray-900);
}

.dark-theme .notion-input {
    background: var(--notion-gray-50);
    border-color: var(--notion-gray-300);
    color: var(--notion-gray-900);
}

.dark-theme .notion-button-secondary {
    background: var(--notion-gray-200);
    color: var(--notion-gray-900);
    border-color: var(--notion-gray-400);
}

/* =============== 工具类系统 =============== */

/* 间距工具类 */
.p-1 { padding: var(--notion-space-1); }
.p-2 { padding: var(--notion-space-2); }
.p-3 { padding: var(--notion-space-3); }
.p-4 { padding: var(--notion-space-4); }
.p-5 { padding: var(--notion-space-5); }
.p-6 { padding: var(--notion-space-6); }
.p-8 { padding: var(--notion-space-8); }

.m-1 { margin: var(--notion-space-1); }
.m-2 { margin: var(--notion-space-2); }
.m-3 { margin: var(--notion-space-3); }
.m-4 { margin: var(--notion-space-4); }
.m-5 { margin: var(--notion-space-5); }
.m-6 { margin: var(--notion-space-6); }
.m-8 { margin: var(--notion-space-8); }

.mb-0 { margin-bottom: 0; }
.mb-2 { margin-bottom: var(--notion-space-2); }
.mb-4 { margin-bottom: var(--notion-space-4); }
.mb-6 { margin-bottom: var(--notion-space-6); }

.mt-0 { margin-top: 0; }
.mt-2 { margin-top: var(--notion-space-2); }
.mt-4 { margin-top: var(--notion-space-4); }
.mt-6 { margin-top: var(--notion-space-6); }

/* 文本工具类 */
.text-xs { font-size: var(--notion-font-size-xs); }
.text-sm { font-size: var(--notion-font-size-sm); }
.text-base { font-size: var(--notion-font-size-base); }
.text-lg { font-size: var(--notion-font-size-lg); }
.text-xl { font-size: var(--notion-font-size-xl); }
.text-2xl { font-size: var(--notion-font-size-2xl); }

.font-normal { font-weight: var(--notion-font-weight-normal); }
.font-medium { font-weight: var(--notion-font-weight-medium); }
.font-semibold { font-weight: var(--notion-font-weight-semibold); }
.font-bold { font-weight: var(--notion-font-weight-bold); }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* 颜色工具类 */
.text-primary { color: var(--notion-primary); }
.text-success { color: var(--notion-success); }
.text-warning { color: var(--notion-warning); }
.text-error { color: var(--notion-error); }
.text-gray-500 { color: var(--notion-gray-500); }
.text-gray-600 { color: var(--notion-gray-600); }
.text-gray-700 { color: var(--notion-gray-700); }
.text-gray-900 { color: var(--notion-gray-900); }

/* 布局工具类 */
.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.block { display: block; }
.inline-block { display: inline-block; }
.hidden { display: none; }

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-center { align-items: center; }
.items-start { align-items: flex-start; }
.items-end { align-items: flex-end; }

.justify-center { justify-content: center; }
.justify-start { justify-content: flex-start; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }

.gap-1 { gap: var(--notion-space-1); }
.gap-2 { gap: var(--notion-space-2); }
.gap-3 { gap: var(--notion-space-3); }
.gap-4 { gap: var(--notion-space-4); }
.gap-6 { gap: var(--notion-space-6); }

/* 边框工具类 */
.border { border: 1px solid var(--notion-gray-300); }
.border-0 { border: none; }
.border-t { border-top: 1px solid var(--notion-gray-300); }
.border-b { border-bottom: 1px solid var(--notion-gray-300); }

.rounded { border-radius: var(--notion-border-radius); }
.rounded-sm { border-radius: var(--notion-border-radius-sm); }
.rounded-lg { border-radius: var(--notion-border-radius-lg); }
.rounded-full { border-radius: var(--notion-border-radius-full); }

/* 阴影工具类 */
.shadow-sm { box-shadow: var(--notion-shadow-sm); }
.shadow { box-shadow: var(--notion-shadow); }
.shadow-md { box-shadow: var(--notion-shadow-md); }
.shadow-lg { box-shadow: var(--notion-shadow-lg); }
.shadow-none { box-shadow: none; }

/* 宽度工具类 */
.w-full { width: 100%; }
.w-auto { width: auto; }
.w-fit { width: fit-content; }

/* 高度工具类 */
.h-auto { height: auto; }
.h-full { height: 100%; }

/* 透明度工具类 */
.opacity-0 { opacity: 0; }
.opacity-50 { opacity: 0.5; }
.opacity-75 { opacity: 0.75; }
.opacity-100 { opacity: 1; }

/* 光标工具类 */
.cursor-pointer { cursor: pointer; }
.cursor-not-allowed { cursor: not-allowed; }
.cursor-default { cursor: default; }

/* 选择工具类 */
.select-none { user-select: none; }
.select-text { user-select: text; }
.select-all { user-select: all; }

/* 溢出工具类 */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

/* 位置工具类 */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

/* Z-index工具类 */
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }

/* =============== 组件特定样式增强 =============== */

/* 关于作者组件样式 */
.about-enhanced-container {
    display: grid;
    gap: var(--notion-space-6);
    margin-top: var(--notion-space-4);
}

.author-card-enhanced {
    background: linear-gradient(135deg, var(--notion-primary-lightest) 0%, white 100%);
    border: 1px solid var(--notion-gray-200);
    border-radius: var(--notion-border-radius-lg);
    padding: var(--notion-space-6);
    box-shadow: var(--notion-shadow-md);
    transition: var(--notion-transition);
}

.author-card-enhanced:hover {
    box-shadow: var(--notion-shadow-lg);
    transform: translateY(-2px);
}

.author-header {
    display: flex;
    align-items: center;
    gap: var(--notion-space-4);
    margin-bottom: var(--notion-space-4);
}

.author-avatar-enhanced {
    position: relative;
    width: 80px;
    height: 80px;
    border-radius: var(--notion-border-radius-full);
    overflow: hidden;
    box-shadow: var(--notion-shadow);
}

.author-avatar-enhanced img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.avatar-badge {
    position: absolute;
    bottom: -2px;
    right: -2px;
    width: 24px;
    height: 24px;
    background: var(--notion-primary);
    border-radius: var(--notion-border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    border: 2px solid white;
}

.author-basic-info {
    flex: 1;
}

.author-name {
    font-size: var(--notion-font-size-xl);
    font-weight: var(--notion-font-weight-bold);
    color: var(--notion-gray-900);
    margin: 0 0 var(--notion-space-1) 0;
}

.author-title {
    font-size: var(--notion-font-size-sm);
    color: var(--notion-primary);
    font-weight: var(--notion-font-weight-medium);
    margin: 0 0 var(--notion-space-3) 0;
}

.author-stats {
    display: flex;
    gap: var(--notion-space-4);
}

.stat-item {
    display: flex;
    align-items: center;
    gap: var(--notion-space-1);
    font-size: var(--notion-font-size-xs);
    color: var(--notion-gray-600);
}

.stat-icon {
    font-size: var(--notion-font-size-sm);
}

.author-description {
    margin-bottom: var(--notion-space-4);
    color: var(--notion-gray-700);
    line-height: var(--notion-line-height-relaxed);
}

.author-links-enhanced {
    display: flex;
    gap: var(--notion-space-3);
    margin-bottom: var(--notion-space-4);
    flex-wrap: wrap;
}

.author-link-enhanced {
    display: flex;
    align-items: center;
    gap: var(--notion-space-2);
    padding: var(--notion-space-2) var(--notion-space-3);
    background: white;
    border: 1px solid var(--notion-gray-200);
    border-radius: var(--notion-border-radius);
    color: var(--notion-gray-700);
    text-decoration: none;
    font-size: var(--notion-font-size-sm);
    font-weight: var(--notion-font-weight-medium);
    transition: var(--notion-transition);
}

.author-link-enhanced:hover {
    background: var(--notion-gray-50);
    border-color: var(--notion-primary);
    color: var(--notion-primary);
    transform: translateY(-1px);
}

.link-arrow {
    opacity: 0;
    transform: translateX(-4px);
    transition: var(--notion-transition);
}

.author-link-enhanced:hover .link-arrow {
    opacity: 1;
    transform: translateX(0);
}

.author-actions {
    display: flex;
    gap: var(--notion-space-2);
    flex-wrap: wrap;
}

.copy-btn {
    display: flex;
    align-items: center;
    gap: var(--notion-space-1);
    padding: var(--notion-space-2) var(--notion-space-3);
    background: var(--notion-gray-100);
    border: 1px solid var(--notion-gray-300);
    border-radius: var(--notion-border-radius);
    color: var(--notion-gray-700);
    font-size: var(--notion-font-size-xs);
    cursor: pointer;
    transition: var(--notion-transition);
}

.copy-btn:hover {
    background: var(--notion-primary-lightest);
    border-color: var(--notion-primary);
    color: var(--notion-primary);
}

/* 插件信息增强样式 */
.plugin-info-enhanced {
    background: white;
    border: 1px solid var(--notion-gray-200);
    border-radius: var(--notion-border-radius-lg);
    padding: var(--notion-space-6);
    box-shadow: var(--notion-shadow);
}

.plugin-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--notion-space-4);
}

.plugin-header h4 {
    font-size: var(--notion-font-size-lg);
    font-weight: var(--notion-font-weight-semibold);
    color: var(--notion-gray-900);
    margin: 0;
}

.plugin-status {
    display: flex;
    align-items: center;
    gap: var(--notion-space-1);
    font-size: var(--notion-font-size-xs);
    color: var(--notion-success);
    font-weight: var(--notion-font-weight-medium);
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: var(--notion-border-radius-full);
    background: var(--notion-gray-400);
}

.status-dot.active {
    background: var(--notion-success);
    animation: pulse 2s ease-in-out infinite;
}

.info-grid-enhanced {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: var(--notion-space-4);
    margin-bottom: var(--notion-space-6);
}

.info-item-enhanced {
    display: flex;
    align-items: center;
    gap: var(--notion-space-3);
    padding: var(--notion-space-3);
    background: var(--notion-gray-50);
    border-radius: var(--notion-border-radius);
    border: 1px solid var(--notion-gray-200);
}

.info-icon {
    font-size: var(--notion-font-size-lg);
    width: 24px;
    text-align: center;
}

.info-content {
    flex: 1;
}

.info-label {
    display: block;
    font-size: var(--notion-font-size-xs);
    color: var(--notion-gray-500);
    font-weight: var(--notion-font-weight-medium);
    margin-bottom: var(--notion-space-1);
}

.info-value {
    display: block;
    font-size: var(--notion-font-size-sm);
    color: var(--notion-gray-900);
    font-weight: var(--notion-font-weight-semibold);
}

.plugin-features h5 {
    font-size: var(--notion-font-size-sm);
    font-weight: var(--notion-font-weight-semibold);
    color: var(--notion-gray-900);
    margin: 0 0 var(--notion-space-3) 0;
}

.features-list {
    display: flex;
    flex-wrap: wrap;
    gap: var(--notion-space-2);
}

.feature-tag {
    display: inline-flex;
    align-items: center;
    padding: var(--notion-space-1) var(--notion-space-2);
    background: var(--notion-primary-lightest);
    color: var(--notion-primary);
    border-radius: var(--notion-border-radius-full);
    font-size: var(--notion-font-size-xs);
    font-weight: var(--notion-font-weight-medium);
    border: 1px solid var(--notion-primary-lighter);
}

/* 致谢信息增强样式 */
.acknowledgments-enhanced {
    background: white;
    border: 1px solid var(--notion-gray-200);
    border-radius: var(--notion-border-radius-lg);
    padding: var(--notion-space-6);
    box-shadow: var(--notion-shadow);
}

.acknowledgments-header h4 {
    font-size: var(--notion-font-size-lg);
    font-weight: var(--notion-font-weight-semibold);
    color: var(--notion-gray-900);
    margin: 0 0 var(--notion-space-2) 0;
}

.acknowledgments-intro {
    color: var(--notion-gray-600);
    font-size: var(--notion-font-size-sm);
    margin: 0 0 var(--notion-space-4) 0;
    line-height: var(--notion-line-height-normal);
}

.reference-projects-enhanced {
    display: grid;
    gap: var(--notion-space-3);
    margin-bottom: var(--notion-space-6);
}

.reference-item-enhanced {
    display: flex;
    align-items: center;
    gap: var(--notion-space-3);
    padding: var(--notion-space-4);
    background: var(--notion-gray-50);
    border: 1px solid var(--notion-gray-200);
    border-radius: var(--notion-border-radius);
    transition: var(--notion-transition);
}

.reference-item-enhanced:hover {
    background: white;
    border-color: var(--notion-primary);
    box-shadow: var(--notion-shadow);
}

.reference-icon {
    font-size: var(--notion-font-size-lg);
    color: var(--notion-primary);
}

.reference-content {
    flex: 1;
}

.reference-name {
    margin: 0 0 var(--notion-space-1) 0;
    font-size: var(--notion-font-size-sm);
    font-weight: var(--notion-font-weight-semibold);
}

.reference-name a {
    color: var(--notion-primary);
    text-decoration: none;
    transition: var(--notion-transition);
}

.reference-name a:hover {
    color: var(--notion-primary-dark);
    text-decoration: underline;
}

.reference-description {
    margin: 0;
    font-size: var(--notion-font-size-xs);
    color: var(--notion-gray-600);
    line-height: var(--notion-line-height-normal);
}

.reference-action {
    display: flex;
    align-items: center;
}

.reference-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    background: var(--notion-primary-lightest);
    border: 1px solid var(--notion-primary-lighter);
    border-radius: var(--notion-border-radius);
    color: var(--notion-primary);
    text-decoration: none;
    transition: var(--notion-transition);
}

.reference-link:hover {
    background: var(--notion-primary);
    color: white;
    transform: scale(1.05);
}

.acknowledgments-footer {
    border-top: 1px solid var(--notion-gray-200);
    padding-top: var(--notion-space-4);
}

.acknowledgments-footer p {
    margin: 0 0 var(--notion-space-4) 0;
    text-align: center;
    color: var(--notion-gray-600);
    font-size: var(--notion-font-size-sm);
}

.community-links {
    display: flex;
    justify-content: center;
    gap: var(--notion-space-4);
    flex-wrap: wrap;
}

.community-link {
    display: flex;
    align-items: center;
    gap: var(--notion-space-1);
    padding: var(--notion-space-2) var(--notion-space-3);
    background: var(--notion-gray-100);
    border: 1px solid var(--notion-gray-300);
    border-radius: var(--notion-border-radius);
    color: var(--notion-gray-700);
    text-decoration: none;
    font-size: var(--notion-font-size-xs);
    font-weight: var(--notion-font-weight-medium);
    transition: var(--notion-transition);
}

.community-link:hover {
    background: var(--notion-primary-lightest);
    border-color: var(--notion-primary);
    color: var(--notion-primary);
}

/* 帮助文档组件增强样式 */
.help-docs-container {
    background: white;
    border-radius: var(--notion-border-radius-lg);
    box-shadow: var(--notion-shadow);
    overflow: hidden;
}

.help-search-bar {
    padding: var(--notion-space-4);
    background: var(--notion-gray-50);
    border-bottom: 1px solid var(--notion-gray-200);
}

.help-search-input {
    width: 100%;
    height: var(--notion-input-height);
    padding: 0 var(--notion-space-3) 0 var(--notion-space-10);
    border: 1px solid var(--notion-gray-300);
    border-radius: var(--notion-border-radius);
    font-size: var(--notion-font-size-sm);
    background: white url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%236b7280" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.35-4.35"></path></svg>') no-repeat var(--notion-space-3) center;
    transition: var(--notion-transition);
}

.help-search-input:focus {
    border-color: var(--notion-primary);
    box-shadow: 0 0 0 3px var(--notion-primary-lighter);
    outline: none;
}

.help-navigation {
    display: flex;
    gap: var(--notion-space-2);
    padding: var(--notion-space-4);
    background: var(--notion-gray-50);
    border-bottom: 1px solid var(--notion-gray-200);
    overflow-x: auto;
}

.help-nav-item {
    display: flex;
    align-items: center;
    gap: var(--notion-space-1);
    padding: var(--notion-space-2) var(--notion-space-3);
    background: white;
    border: 1px solid var(--notion-gray-300);
    border-radius: var(--notion-border-radius);
    color: var(--notion-gray-700);
    font-size: var(--notion-font-size-xs);
    font-weight: var(--notion-font-weight-medium);
    cursor: pointer;
    transition: var(--notion-transition);
    white-space: nowrap;
}

.help-nav-item:hover {
    background: var(--notion-gray-100);
    border-color: var(--notion-gray-400);
}

.help-nav-item.active {
    background: var(--notion-primary);
    border-color: var(--notion-primary);
    color: white;
}

.help-content {
    padding: var(--notion-space-6);
    max-height: 600px;
    overflow-y: auto;
}

.help-section {
    margin-bottom: var(--notion-space-6);
    border: 1px solid var(--notion-gray-200);
    border-radius: var(--notion-border-radius);
    overflow: hidden;
}

.help-section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--notion-space-4);
    background: var(--notion-gray-50);
    border-bottom: 1px solid var(--notion-gray-200);
    cursor: pointer;
    transition: var(--notion-transition);
}

.help-section-header:hover {
    background: var(--notion-gray-100);
}

.help-section-title {
    font-size: var(--notion-font-size-base);
    font-weight: var(--notion-font-weight-semibold);
    color: var(--notion-gray-900);
    margin: 0;
}

.help-section-toggle {
    font-size: var(--notion-font-size-lg);
    color: var(--notion-gray-500);
    transition: var(--notion-transition);
}

.help-section.expanded .help-section-toggle {
    transform: rotate(180deg);
}

.help-section-content {
    padding: var(--notion-space-4);
    background: white;
    display: none;
}

.help-section.expanded .help-section-content {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

.help-section-content h4 {
    font-size: var(--notion-font-size-sm);
    font-weight: var(--notion-font-weight-semibold);
    color: var(--notion-gray-900);
    margin: 0 0 var(--notion-space-2) 0;
}

.help-section-content p {
    font-size: var(--notion-font-size-sm);
    color: var(--notion-gray-700);
    line-height: var(--notion-line-height-relaxed);
    margin: 0 0 var(--notion-space-3) 0;
}

.help-section-content ul {
    margin: 0 0 var(--notion-space-3) var(--notion-space-4);
    padding: 0;
}

.help-section-content li {
    font-size: var(--notion-font-size-sm);
    color: var(--notion-gray-700);
    line-height: var(--notion-line-height-relaxed);
    margin-bottom: var(--notion-space-1);
}

.help-highlight {
    background: var(--notion-warning-light);
    padding: 0 var(--notion-space-1);
    border-radius: var(--notion-border-radius-sm);
    font-weight: var(--notion-font-weight-medium);
}
