/**
 * Notion to WordPress - Web Components 样式
 * 现代化组件样式系统
 */

/* =============== CSS 变量定义 =============== */
:root {
    /* 主色调 */
    --notion-primary: #2563eb;
    --notion-primary-dark: #1d4ed8;
    --notion-primary-light: #3b82f6;
    
    /* 状态颜色 */
    --notion-success: #10b981;
    --notion-warning: #f59e0b;
    --notion-error: #ef4444;
    --notion-info: #06b6d4;
    
    /* 灰度色彩 */
    --notion-gray-50: #f9fafb;
    --notion-gray-100: #f3f4f6;
    --notion-gray-200: #e5e7eb;
    --notion-gray-300: #d1d5db;
    --notion-gray-400: #9ca3af;
    --notion-gray-500: #6b7280;
    --notion-gray-600: #4b5563;
    --notion-gray-700: #374151;
    --notion-gray-800: #1f2937;
    --notion-gray-900: #111827;
    
    /* 布局 */
    --notion-border-radius: 8px;
    --notion-border-radius-lg: 12px;
    --notion-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --notion-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* 动画 */
    --notion-transition: all 0.2s ease-in-out;
    --notion-transition-fast: all 0.1s ease-in-out;
}

/* =============== 基础组件样式 =============== */
.notion-tab-manager {
    width: 100%;
    background: white;
    border-radius: var(--notion-border-radius-lg);
    box-shadow: var(--notion-shadow-lg);
    overflow: hidden;
}

/* 标签页导航 */
.tab-navigation {
    display: flex;
    background: var(--notion-gray-50);
    border-bottom: 1px solid var(--notion-gray-200);
    padding: 0;
    margin: 0;
    overflow-x: auto;
    scrollbar-width: none;
    -ms-overflow-style: none;
}

.tab-navigation::-webkit-scrollbar {
    display: none;
}

.tab-button {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 20px;
    background: transparent;
    border: none;
    border-bottom: 3px solid transparent;
    color: var(--notion-gray-600);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--notion-transition);
    white-space: nowrap;
    min-width: fit-content;
}

.tab-button:hover {
    background: var(--notion-gray-100);
    color: var(--notion-gray-800);
}

.tab-button.active {
    background: white;
    color: var(--notion-primary);
    border-bottom-color: var(--notion-primary);
}

.tab-icon {
    font-size: 16px;
}

.tab-label {
    font-weight: 500;
}

/* 标签页内容容器 */
.tab-content-container {
    position: relative;
    min-height: 500px;
}

.tab-content {
    display: none;
    padding: 24px;
    animation: fadeIn 0.3s ease-in-out;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 占位符样式 */
.tab-placeholder,
.settings-placeholder {
    padding: 40px;
    text-align: center;
    color: var(--notion-gray-500);
    background: var(--notion-gray-50);
    border-radius: var(--notion-border-radius);
    border: 2px dashed var(--notion-gray-200);
}

/* =============== 性能监控组件样式 =============== */
.notion-performance-monitor {
    width: 100%;
}

.performance-header {
    margin-bottom: 24px;
}

.performance-header h2 {
    margin: 0 0 8px 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--notion-gray-900);
}

.performance-header p {
    margin: 0 0 16px 0;
    color: var(--notion-gray-600);
    font-size: 14px;
    line-height: 1.5;
}

.performance-actions {
    display: flex;
    gap: 12px;
}

.btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: var(--notion-border-radius);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: var(--notion-transition);
    text-decoration: none;
}

.btn-primary {
    background: var(--notion-primary);
    color: white;
    border-color: var(--notion-primary);
}

.btn-primary:hover {
    background: var(--notion-primary-dark);
    border-color: var(--notion-primary-dark);
}

.btn-secondary {
    background: white;
    color: var(--notion-gray-700);
    border-color: var(--notion-gray-300);
}

.btn-secondary:hover {
    background: var(--notion-gray-50);
    border-color: var(--notion-gray-400);
}

/* 状态卡片 */
.status-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;
    margin-bottom: 24px;
}

.status-card {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    background: white;
    border: 1px solid var(--notion-gray-200);
    border-radius: var(--notion-border-radius-lg);
    box-shadow: var(--notion-shadow);
    transition: var(--notion-transition);
}

.status-card:hover {
    box-shadow: var(--notion-shadow-lg);
    transform: translateY(-2px);
}

.status-card.idle {
    border-left: 4px solid var(--notion-gray-400);
}

.status-card.running,
.status-card.processing {
    border-left: 4px solid var(--notion-info);
}

.status-card.success {
    border-left: 4px solid var(--notion-success);
}

.status-card.error {
    border-left: 4px solid var(--notion-error);
}

.card-icon {
    font-size: 24px;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: var(--notion-gray-100);
    border-radius: var(--notion-border-radius);
}

.card-content h3 {
    margin: 0 0 4px 0;
    font-size: 14px;
    font-weight: 500;
    color: var(--notion-gray-600);
}

.status-value {
    font-size: 24px;
    font-weight: 700;
    color: var(--notion-gray-900);
    margin-bottom: 4px;
}

.status-detail {
    font-size: 12px;
    color: var(--notion-gray-500);
}

/* 性能监控部分 */
.performance-section {
    margin-bottom: 32px;
    padding: 24px;
    background: white;
    border: 1px solid var(--notion-gray-200);
    border-radius: var(--notion-border-radius-lg);
}

.performance-section h3 {
    margin: 0 0 16px 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--notion-gray-900);
}

/* 异步状态信息 */
.async-status-container {
    display: grid;
    gap: 16px;
}

.status-info {
    display: grid;
    gap: 8px;
}

.info-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid var(--notion-gray-100);
}

.info-row:last-child {
    border-bottom: none;
}

.info-row .label {
    font-weight: 500;
    color: var(--notion-gray-700);
}

.info-row .value {
    color: var(--notion-gray-900);
}

.value.status-idle {
    color: var(--notion-gray-500);
}

.value.status-running {
    color: var(--notion-info);
}

.value.status-error {
    color: var(--notion-error);
}

.value.status-completed {
    color: var(--notion-success);
}

/* 进度条 */
.progress-container {
    margin-top: 16px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--notion-gray-200);
    border-radius: 4px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--notion-primary), var(--notion-primary-light));
    transition: width 0.3s ease;
}

.progress-text {
    text-align: center;
    margin-top: 8px;
    font-size: 12px;
    color: var(--notion-gray-600);
}

/* 队列状态统计 */
.queue-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 16px;
}

.stat-item {
    text-align: center;
    padding: 16px;
    background: var(--notion-gray-50);
    border-radius: var(--notion-border-radius);
    border: 1px solid var(--notion-gray-200);
}

.stat-item.processing {
    background: rgba(6, 182, 212, 0.1);
    border-color: var(--notion-info);
}

.stat-item.success {
    background: rgba(16, 185, 129, 0.1);
    border-color: var(--notion-success);
}

.stat-item.error {
    background: rgba(239, 68, 68, 0.1);
    border-color: var(--notion-error);
}

.stat-item.warning {
    background: rgba(245, 158, 11, 0.1);
    border-color: var(--notion-warning);
}

.stat-value {
    font-size: 20px;
    font-weight: 700;
    color: var(--notion-gray-900);
    margin-bottom: 4px;
}

.stat-label {
    font-size: 12px;
    color: var(--notion-gray-600);
    font-weight: 500;
}

/* 加载和错误状态 */
.performance-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px;
    color: var(--notion-gray-600);
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid var(--notion-gray-200);
    border-top: 3px solid var(--notion-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 14px;
}

.performance-error {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 16px;
    background: rgba(239, 68, 68, 0.1);
    border: 1px solid var(--notion-error);
    border-radius: var(--notion-border-radius);
    color: var(--notion-error);
    margin-top: 16px;
}

.error-icon {
    font-size: 20px;
}

.error-message {
    flex: 1;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tab-navigation {
        flex-wrap: wrap;
    }
    
    .tab-button {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }
    
    .status-cards {
        grid-template-columns: 1fr;
    }
    
    .queue-stats {
        grid-template-columns: repeat(2, 1fr);
    }
    
    .tab-content {
        padding: 16px;
    }
}
