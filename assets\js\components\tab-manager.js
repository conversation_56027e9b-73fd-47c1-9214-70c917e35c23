/**
 * Notion to WordPress - 标签页管理器组件
 * 管理所有标签页的切换和状态
 */

class NotionTabManager extends NotionBaseComponent {
    constructor() {
        super();
        this.state = {
            activeTab: 'api-settings', // 默认激活API设置，与jQuery版本保持一致
            tabs: [],
            initialized: false,
            animationDuration: 300, // 动画持续时间
            isTransitioning: false // 防止快速切换时的冲突
        };

        // 标签页配置，与现有系统完全兼容
        this.tabConfigs = this.getTabConfigs();

        // 兼容性标志
        this.jqueryCompatMode = true; // 启用jQuery兼容模式
    }

    /**
     * 获取标签页配置
     * 与现有HTML结构和jQuery实现保持一致
     */
    getTabConfigs() {
        return [
            {
                id: 'api-settings',
                label: '🔄 同步设置',
                icon: '🔄',
                component: 'notion-api-settings',
                title: '配置Notion API连接和同步设置',
                order: 1
            },
            {
                id: 'field-mapping',
                label: '🔗 字段映射',
                icon: '🔗',
                component: 'notion-field-mapping',
                title: '配置Notion字段与WordPress字段的映射关系',
                order: 2
            },
            {
                id: 'other-settings',
                label: '⚙️ 其他设置',
                icon: '⚙️',
                component: 'notion-other-settings',
                title: '配置插件的其他选项和功能',
                order: 3
            },
            {
                id: 'performance',
                label: '📊 性能监控',
                icon: '📊',
                component: 'notion-performance-monitor',
                title: '查看性能统计和监控信息',
                order: 4
            },
            {
                id: 'debug',
                label: '🐞 调试工具',
                icon: '🐞',
                component: 'notion-debug-tools',
                title: '查看日志、调试信息和故障排除工具',
                order: 5
            },
            {
                id: 'help',
                label: '📖 使用帮助',
                icon: '📖',
                component: 'notion-help-docs',
                title: '查看使用说明、常见问题和故障排除指南',
                order: 6
            },
            {
                id: 'about-author',
                label: '👨‍💻 关于作者',
                icon: '👨‍💻',
                component: 'notion-about-author',
                title: '了解插件作者和项目信息',
                order: 7
            }
        ];
    }

    init() {
        this.className = 'notion-tab-manager';

        // 初始化标签页配置
        this.setState({ tabs: this.tabConfigs });

        // 恢复上次活动的标签页
        this.restoreLastActiveTab();

        // 如果启用jQuery兼容模式，监听现有的标签页切换
        if (this.jqueryCompatMode) {
            this.setupJQueryCompatibility();
        }

        // 订阅组件间通信事件
        this.subscribe('tab:switch', this.handleTabSwitchRequest.bind(this));
        this.subscribe('tab:refresh', this.handleTabRefresh.bind(this));

        if (this.config.debugMode) {
            console.log('NotionTabManager: 初始化完成', {
                activeTab: this.state.activeTab,
                tabCount: this.state.tabs.length,
                jqueryCompatMode: this.jqueryCompatMode
            });
        }
    }

    render() {
        // 如果启用jQuery兼容模式，不渲染自己的HTML，而是增强现有结构
        if (this.jqueryCompatMode) {
            this.enhanceExistingTabs();
            return;
        }

        // 标准Web Components渲染模式
        this.innerHTML = `
            <div class="notion-tab-navigation">
                ${this.renderTabButtons()}
            </div>
            <div class="notion-tab-content-container">
                ${this.renderTabContents()}
            </div>
        `;
    }

    /**
     * 增强现有的标签页结构
     * 与现有jQuery实现协同工作
     */
    enhanceExistingTabs() {
        // 查找现有的标签页按钮和内容
        const existingButtons = document.querySelectorAll('.notion-wp-menu-item');
        const existingContents = document.querySelectorAll('.notion-wp-tab-content');

        if (existingButtons.length === 0 || existingContents.length === 0) {
            console.warn('NotionTabManager: 未找到现有的标签页结构，切换到标准模式');
            this.jqueryCompatMode = false;
            this.render();
            return;
        }

        // 为现有按钮添加增强功能
        existingButtons.forEach(button => {
            const tabId = button.dataset.tab;
            if (tabId) {
                // 添加键盘导航支持
                button.setAttribute('tabindex', '0');
                button.setAttribute('role', 'tab');

                // 添加工具提示
                const tabConfig = this.tabConfigs.find(config => config.id === tabId);
                if (tabConfig && tabConfig.title) {
                    button.setAttribute('title', tabConfig.title);
                }
            }
        });

        // 为现有内容区域添加增强功能
        existingContents.forEach(content => {
            const tabId = content.id;
            if (tabId) {
                content.setAttribute('role', 'tabpanel');
                content.setAttribute('aria-labelledby', `tab-${tabId}`);
            }
        });

        if (this.config.debugMode) {
            console.log('NotionTabManager: 现有标签页结构增强完成', {
                buttons: existingButtons.length,
                contents: existingContents.length
            });
        }
    }

    renderTabButtons() {
        return this.state.tabs.map(tab => `
            <button 
                class="tab-button ${tab.id === this.state.activeTab ? 'active' : ''}"
                data-tab="${tab.id}"
                title="${tab.title}"
            >
                <span class="tab-icon">${tab.icon}</span>
                <span class="tab-label">${tab.label}</span>
            </button>
        `).join('');
    }

    renderTabContents() {
        return this.state.tabs.map(tab => `
            <div 
                class="tab-content ${tab.id === this.state.activeTab ? 'active' : ''}"
                id="tab-${tab.id}"
                data-tab="${tab.id}"
            >
                ${this.renderTabContent(tab)}
            </div>
        `).join('');
    }

    renderTabContent(tab) {
        switch (tab.id) {
            case 'performance':
                return '<notion-performance-monitor></notion-performance-monitor>';
            case 'api-settings':
                return this.renderApiSettings();
            case 'field-mapping':
                return this.renderFieldMapping();
            case 'other-settings':
                return this.renderOtherSettings();
            case 'debug':
                return this.renderDebug();
            case 'help':
                return this.renderHelp();
            case 'about-author':
                return this.renderAboutAuthor();
            default:
                return `<div class="tab-placeholder">标签页 ${tab.label} 的内容正在开发中...</div>`;
        }
    }

    renderApiSettings() {
        return `
            <div class="settings-section">
                <h2>🔑 API 设置</h2>
                <p>配置您的 Notion API 密钥和数据库连接。</p>
                <div class="settings-placeholder">
                    API设置内容将在后续步骤中实现...
                </div>
            </div>
        `;
    }

    renderFieldMapping() {
        return `
            <div class="settings-section">
                <h2>🔗 字段映射</h2>
                <p>配置 Notion 字段与 WordPress 字段的映射关系。</p>
                <div class="settings-placeholder">
                    字段映射内容将在后续步骤中实现...
                </div>
            </div>
        `;
    }

    renderOtherSettings() {
        return `
            <div class="settings-section">
                <h2>⚙️ 其他设置</h2>
                <p>配置插件的其他选项和功能。</p>
                <div class="settings-placeholder">
                    其他设置内容将在后续步骤中实现...
                </div>
            </div>
        `;
    }

    renderDebug() {
        return `
            <div class="settings-section">
                <h2>🐛 调试工具</h2>
                <p>查看日志、调试信息和故障排除工具。</p>
                <div class="settings-placeholder">
                    调试工具内容将在后续步骤中实现...
                </div>
            </div>
        `;
    }

    renderHelp() {
        return `
            <div class="settings-section">
                <h2>❓ 帮助文档</h2>
                <p>查看使用说明、常见问题和故障排除指南。</p>
                <div class="settings-placeholder">
                    帮助文档内容将在后续步骤中实现...
                </div>
            </div>
        `;
    }

    renderAboutAuthor() {
        return `
            <div class="settings-section">
                <h2>👨‍💻 关于作者</h2>
                <p>了解插件作者和项目信息。</p>
                <div class="settings-placeholder">
                    关于作者内容将在后续步骤中实现...
                </div>
            </div>
        `;
    }

    bindEvents() {
        // 只在非jQuery兼容模式下绑定自己的事件
        if (!this.jqueryCompatMode) {
            // 绑定标签页按钮点击事件
            this.$$('.tab-button').forEach(button => {
                this.addEventListenerManaged(button, 'click', (e) => {
                    const tabId = e.currentTarget.dataset.tab;
                    this.switchTab(tabId, { source: 'button-click' });
                });
            });
        }

        // 全局键盘快捷键（两种模式都支持）
        this.addEventListenerManaged(document, 'keydown', (e) => {
            // Ctrl + 数字键切换标签页
            if (e.ctrlKey && e.key >= '1' && e.key <= '9') {
                e.preventDefault();
                const tabIndex = parseInt(e.key) - 1;
                if (this.state.tabs[tabIndex]) {
                    this.switchTab(this.state.tabs[tabIndex].id, { source: 'keyboard' });
                }
            }

            // Alt + 左右箭头键切换标签页
            if (e.altKey && (e.key === 'ArrowLeft' || e.key === 'ArrowRight')) {
                e.preventDefault();
                const currentIndex = this.state.tabs.findIndex(tab => tab.id === this.state.activeTab);
                let nextIndex;

                if (e.key === 'ArrowLeft') {
                    nextIndex = currentIndex > 0 ? currentIndex - 1 : this.state.tabs.length - 1;
                } else {
                    nextIndex = currentIndex < this.state.tabs.length - 1 ? currentIndex + 1 : 0;
                }

                this.switchTab(this.state.tabs[nextIndex].id, { source: 'keyboard-nav' });
            }
        });

        // 监听窗口焦点事件，用于同步多窗口状态
        this.addEventListenerManaged(window, 'focus', () => {
            this.syncWithLocalStorage();
        });

        // 监听页面可见性变化
        this.addEventListenerManaged(document, 'visibilitychange', () => {
            if (!document.hidden) {
                this.syncWithLocalStorage();
            }
        });

        if (this.config.debugMode) {
            console.log('NotionTabManager: 事件绑定完成', {
                jqueryCompatMode: this.jqueryCompatMode,
                keyboardShortcuts: true,
                windowSync: true
            });
        }
    }

    /**
     * 与localStorage同步状态
     */
    syncWithLocalStorage() {
        try {
            const savedTab = localStorage.getItem('notion_wp_active_tab');
            if (savedTab && savedTab !== this.state.activeTab) {
                const tabExists = this.state.tabs.find(tab => tab.id === savedTab);
                if (tabExists) {
                    this.updateInternalState(savedTab);

                    if (this.config.debugMode) {
                        console.log(`NotionTabManager: 从localStorage同步标签页 ${savedTab}`);
                    }
                }
            }
        } catch (error) {
            console.warn('NotionTabManager: localStorage同步失败', error);
        }
    }

    // ==================== 公共API方法 ====================

    /**
     * 获取当前活动标签页ID
     */
    getActiveTab() {
        return this.state.activeTab;
    }

    /**
     * 获取标签页信息
     */
    getTabInfo(tabId) {
        return this.state.tabs.find(tab => tab.id === tabId) || null;
    }

    /**
     * 获取所有标签页配置
     */
    getAllTabs() {
        return [...this.state.tabs];
    }

    /**
     * 检查标签页是否存在
     */
    hasTab(tabId) {
        return this.state.tabs.some(tab => tab.id === tabId);
    }

    /**
     * 获取下一个标签页ID
     */
    getNextTab(currentTabId = this.state.activeTab) {
        const currentIndex = this.state.tabs.findIndex(tab => tab.id === currentTabId);
        const nextIndex = currentIndex < this.state.tabs.length - 1 ? currentIndex + 1 : 0;
        return this.state.tabs[nextIndex].id;
    }

    /**
     * 获取上一个标签页ID
     */
    getPreviousTab(currentTabId = this.state.activeTab) {
        const currentIndex = this.state.tabs.findIndex(tab => tab.id === currentTabId);
        const prevIndex = currentIndex > 0 ? currentIndex - 1 : this.state.tabs.length - 1;
        return this.state.tabs[prevIndex].id;
    }

    /**
     * 切换到下一个标签页
     */
    switchToNext() {
        const nextTabId = this.getNextTab();
        return this.switchTab(nextTabId, { source: 'next-navigation' });
    }

    /**
     * 切换到上一个标签页
     */
    switchToPrevious() {
        const prevTabId = this.getPreviousTab();
        return this.switchTab(prevTabId, { source: 'prev-navigation' });
    }

    /**
     * 刷新当前标签页
     */
    refreshCurrentTab() {
        return this.switchTab(this.state.activeTab, { force: true, source: 'refresh' });
    }

    /**
     * 获取标签页统计信息
     */
    getTabStats() {
        return {
            totalTabs: this.state.tabs.length,
            activeTab: this.state.activeTab,
            activeTabIndex: this.state.tabs.findIndex(tab => tab.id === this.state.activeTab),
            isTransitioning: this.state.isTransitioning,
            jqueryCompatMode: this.jqueryCompatMode
        };
    }

    /**
     * 设置jQuery兼容性支持
     * 监听现有的jQuery标签页切换事件
     */
    setupJQueryCompatibility() {
        // 监听jQuery标签页切换事件
        if (typeof $ !== 'undefined') {
            // 拦截现有的点击事件，添加我们的增强功能
            $(document).on('click.notionTabManager', '.notion-wp-menu-item', (e) => {
                const tabId = $(e.currentTarget).data('tab');
                if (tabId) {
                    // 更新我们的状态，但不阻止jQuery的原有逻辑
                    this.updateInternalState(tabId);
                }
            });

            // 监听localStorage变化（其他标签页或窗口的变化）
            window.addEventListener('storage', (e) => {
                if (e.key === 'notion_wp_active_tab' && e.newValue) {
                    this.updateInternalState(e.newValue);
                }
            });

            if (this.config.debugMode) {
                console.log('NotionTabManager: jQuery兼容性设置完成');
            }
        } else {
            console.warn('NotionTabManager: jQuery未找到，禁用兼容模式');
            this.jqueryCompatMode = false;
        }
    }

    /**
     * 更新内部状态（不触发UI更新，由jQuery处理）
     */
    updateInternalState(tabId) {
        if (!this.state.tabs.find(tab => tab.id === tabId)) {
            console.warn(`NotionTabManager: 标签页 ${tabId} 不存在`);
            return;
        }

        const previousTab = this.state.activeTab;
        this.setState({ activeTab: tabId });

        // 广播标签页切换事件
        this.broadcast('tab:changed', {
            tabId,
            previousTab,
            timestamp: Date.now(),
            source: 'jquery-compat'
        });

        // 触发特定标签页的激活事件
        this.broadcast(`tab:${tabId}:activated`, {
            tabId,
            previousTab
        });

        if (this.config.debugMode) {
            console.log(`NotionTabManager: 内部状态已更新 ${previousTab} -> ${tabId}`);
        }
    }

    /**
     * 增强的标签页切换方法
     * 支持动画、验证和事件广播
     */
    switchTab(tabId, options = {}) {
        const {
            animate = true,
            force = false,
            source = 'api'
        } = options;

        // 验证标签页是否存在
        const targetTab = this.state.tabs.find(tab => tab.id === tabId);
        if (!targetTab) {
            console.warn(`NotionTabManager: 标签页 ${tabId} 不存在`);
            return false;
        }

        // 检查是否正在切换中
        if (this.state.isTransitioning && !force) {
            console.warn('NotionTabManager: 标签页正在切换中，请稍后再试');
            return false;
        }

        // 如果已经是当前标签页，直接返回
        if (this.state.activeTab === tabId && !force) {
            if (this.config.debugMode) {
                console.log(`NotionTabManager: 标签页 ${tabId} 已经是活动状态`);
            }
            return true;
        }

        const previousTab = this.state.activeTab;

        // 设置切换状态
        this.setState({ isTransitioning: true });

        try {
            // 如果启用jQuery兼容模式，触发jQuery的切换逻辑
            if (this.jqueryCompatMode && typeof $ !== 'undefined') {
                const $targetButton = $(`.notion-wp-menu-item[data-tab="${tabId}"]`);
                if ($targetButton.length > 0) {
                    $targetButton.trigger('click');
                } else {
                    console.warn(`NotionTabManager: 未找到标签页按钮 ${tabId}`);
                    this.performDirectSwitch(tabId, animate);
                }
            } else {
                // 直接切换模式
                this.performDirectSwitch(tabId, animate);
            }

            // 更新状态
            this.setState({ activeTab: tabId });

            // 保存到localStorage
            this.saveActiveTab(tabId);

            // 广播事件
            this.broadcast('tab:switched', {
                tabId,
                previousTab,
                source,
                timestamp: Date.now()
            });

            // 发送自定义事件
            this.emit('tab-changed', {
                tabId,
                previousTab,
                tabInfo: targetTab,
                source
            });

            if (this.config.debugMode) {
                console.log(`NotionTabManager: 标签页切换完成 ${previousTab} -> ${tabId}`);
            }

            return true;

        } catch (error) {
            this.handleError('标签页切换失败', error);
            return false;
        } finally {
            // 重置切换状态
            setTimeout(() => {
                this.setState({ isTransitioning: false });
            }, this.state.animationDuration);
        }
    }

    /**
     * 直接执行标签页切换（不依赖jQuery）
     */
    performDirectSwitch(tabId, animate = true) {
        // 更新按钮状态
        this.updateTabButtons(tabId);

        // 更新内容区域
        this.updateTabContents(tabId, animate);

        // 更新状态指示器
        this.updateStatusIndicator(tabId);
    }

    /**
     * 更新标签页按钮状态
     */
    updateTabButtons(activeTabId) {
        // 更新Web Components按钮
        this.$$('.tab-button').forEach(button => {
            const isActive = button.dataset.tab === activeTabId;
            button.classList.toggle('active', isActive);
            button.setAttribute('aria-selected', isActive.toString());
        });

        // 更新jQuery兼容模式下的按钮
        if (this.jqueryCompatMode && typeof $ !== 'undefined') {
            $('.notion-wp-menu-item').removeClass('active');
            $(`.notion-wp-menu-item[data-tab="${activeTabId}"]`).addClass('active');
        }
    }

    /**
     * 更新标签页内容区域
     */
    updateTabContents(activeTabId, animate = true) {
        // 更新Web Components内容
        this.$$('.tab-content').forEach(content => {
            const isActive = content.dataset.tab === activeTabId;
            content.classList.toggle('active', isActive);
            content.setAttribute('aria-hidden', (!isActive).toString());
        });

        // 更新jQuery兼容模式下的内容（复制原有逻辑）
        if (this.jqueryCompatMode && typeof $ !== 'undefined') {
            // 隐藏所有标签页内容
            $('.notion-wp-tab-content').removeClass('active')
                                      .hide()
                                      .css({
                                        'display': 'none !important',
                                        'visibility': 'hidden !important',
                                        'opacity': '0 !important'
                                      });

            // 显示目标标签页
            const $targetTab = $('#' + activeTabId);
            $targetTab.addClass('active')
                      .show()
                      .css({
                        'display': 'block !important',
                        'visibility': 'visible !important',
                        'opacity': '1 !important',
                        'height': 'auto !important',
                        'min-height': '500px !important',
                        'overflow': 'visible !important'
                      });

            // 特殊处理性能监控标签页
            if (activeTabId === 'performance') {
                setTimeout(() => {
                    if ($('#async-status-container').length > 0 && typeof refreshAsyncStatus === 'function') {
                        refreshAsyncStatus();
                    }
                }, 350);
            }
        }
    }

    /**
     * 更新状态指示器
     */
    updateStatusIndicator(activeTabId) {
        const statusElement = document.getElementById('current-tab-name');
        if (statusElement) {
            statusElement.textContent = activeTabId;
        }
    }

    /**
     * 处理标签页切换请求事件
     */
    handleTabSwitchRequest(data) {
        const { tabId, options = {} } = data;
        this.switchTab(tabId, { ...options, source: 'event-bus' });
    }

    /**
     * 处理标签页刷新事件
     */
    handleTabRefresh(data) {
        const { tabId = this.state.activeTab } = data;

        // 强制重新切换到当前标签页以刷新内容
        this.switchTab(tabId, { force: true, source: 'refresh' });

        // 广播刷新完成事件
        this.broadcast('tab:refreshed', { tabId, timestamp: Date.now() });
    }

    /**
     * 保存活动标签页到localStorage
     */
    saveActiveTab(tabId) {
        try {
            localStorage.setItem('notion_wp_active_tab', tabId);

            // 保存时间戳用于调试
            localStorage.setItem('notion_wp_active_tab_timestamp', Date.now().toString());

            if (this.config.debugMode) {
                console.log(`NotionTabManager: 已保存活动标签页 ${tabId} 到localStorage`);
            }
        } catch (error) {
            console.warn('NotionTabManager: 保存活动标签页到localStorage失败', error);
        }
    }

    /**
     * 从localStorage恢复上次活动的标签页
     */
    restoreLastActiveTab() {
        try {
            const lastActiveTab = localStorage.getItem('notion_wp_active_tab');

            if (lastActiveTab) {
                // 验证标签页是否存在
                const tabExists = this.state.tabs.find(tab => tab.id === lastActiveTab);

                if (tabExists) {
                    this.setState({ activeTab: lastActiveTab });

                    if (this.config.debugMode) {
                        console.log(`NotionTabManager: 恢复上次活动标签页 ${lastActiveTab}`);
                    }
                } else {
                    console.warn(`NotionTabManager: 上次活动标签页 ${lastActiveTab} 不存在，使用默认标签页`);
                    this.saveActiveTab(this.state.activeTab); // 保存默认标签页
                }
            } else {
                if (this.config.debugMode) {
                    console.log(`NotionTabManager: 未找到保存的标签页，使用默认标签页 ${this.state.activeTab}`);
                }
                this.saveActiveTab(this.state.activeTab); // 保存默认标签页
            }
        } catch (error) {
            console.warn('NotionTabManager: 从localStorage恢复活动标签页失败', error);
        }
    }

    /**
     * 清除localStorage中的标签页数据
     */
    clearSavedTab() {
        try {
            localStorage.removeItem('notion_wp_active_tab');
            localStorage.removeItem('notion_wp_active_tab_timestamp');

            if (this.config.debugMode) {
                console.log('NotionTabManager: 已清除localStorage中的标签页数据');
            }
        } catch (error) {
            console.warn('NotionTabManager: 清除localStorage标签页数据失败', error);
        }
    }

    // ==================== 动态标签页管理 ====================

    /**
     * 添加新标签页
     */
    addTab(tabConfig) {
        if (!tabConfig || !tabConfig.id) {
            throw new Error('标签页配置必须包含id字段');
        }

        const existingTab = this.state.tabs.find(tab => tab.id === tabConfig.id);
        if (existingTab) {
            console.warn(`NotionTabManager: 标签页 ${tabConfig.id} 已存在`);
            return false;
        }

        const newTab = {
            id: tabConfig.id,
            label: tabConfig.label || tabConfig.id,
            icon: tabConfig.icon || '📄',
            component: tabConfig.component || null,
            title: tabConfig.title || tabConfig.label || tabConfig.id,
            order: tabConfig.order || this.state.tabs.length + 1,
            ...tabConfig
        };

        this.setState({
            tabs: [...this.state.tabs, newTab].sort((a, b) => (a.order || 0) - (b.order || 0))
        });

        // 广播标签页添加事件
        this.broadcast('tab:added', { tabConfig: newTab });

        if (this.config.debugMode) {
            console.log(`NotionTabManager: 已添加标签页 ${newTab.id}`);
        }

        return true;
    }

    /**
     * 移除标签页
     */
    removeTab(tabId) {
        const tabIndex = this.state.tabs.findIndex(tab => tab.id === tabId);
        if (tabIndex === -1) {
            console.warn(`NotionTabManager: 标签页 ${tabId} 不存在`);
            return false;
        }

        const removedTab = this.state.tabs[tabIndex];
        const newTabs = [...this.state.tabs];
        newTabs.splice(tabIndex, 1);

        // 如果删除的是当前活动标签页，切换到第一个标签页
        let newActiveTab = this.state.activeTab;
        if (this.state.activeTab === tabId && newTabs.length > 0) {
            newActiveTab = newTabs[0].id;
            this.switchTab(newActiveTab, { source: 'tab-removed' });
        }

        this.setState({
            tabs: newTabs,
            activeTab: newActiveTab
        });

        // 广播标签页移除事件
        this.broadcast('tab:removed', { tabId, removedTab });

        if (this.config.debugMode) {
            console.log(`NotionTabManager: 已移除标签页 ${tabId}`);
        }

        return true;
    }

    /**
     * 更新标签页配置
     */
    updateTab(tabId, updates) {
        const tabIndex = this.state.tabs.findIndex(tab => tab.id === tabId);
        if (tabIndex === -1) {
            console.warn(`NotionTabManager: 标签页 ${tabId} 不存在`);
            return false;
        }

        const newTabs = [...this.state.tabs];
        newTabs[tabIndex] = { ...newTabs[tabIndex], ...updates };

        this.setState({ tabs: newTabs });

        // 广播标签页更新事件
        this.broadcast('tab:updated', { tabId, updates });

        if (this.config.debugMode) {
            console.log(`NotionTabManager: 已更新标签页 ${tabId}`, updates);
        }

        return true;
    }
}

// ==================== 组件注册和导出 ====================

// 注册Web Component
if (!customElements.get('notion-tab-manager')) {
    customElements.define('notion-tab-manager', NotionTabManager);
}

// 注册到Notion组件注册表
if (window.NotionRegistry) {
    window.NotionRegistry.register('notion-tab-manager', NotionTabManager);
} else {
    console.warn('NotionTabManager: NotionRegistry未找到，使用标准Web Components注册');
}

// 导出到全局作用域
window.NotionTabManager = NotionTabManager;

// 创建全局实例（单例模式）
if (!window.notionTabManagerInstance) {
    // 延迟创建实例，确保DOM已加载
    document.addEventListener('DOMContentLoaded', () => {
        // 查找现有的标签页管理器元素
        let tabManagerElement = document.querySelector('notion-tab-manager');

        if (!tabManagerElement) {
            // 如果不存在，创建一个新的
            tabManagerElement = document.createElement('notion-tab-manager');

            // 查找合适的插入位置
            const adminContainer = document.querySelector('.notion-wp-admin-container') ||
                                 document.querySelector('.wrap') ||
                                 document.body;

            if (adminContainer) {
                adminContainer.appendChild(tabManagerElement);
            }
        }

        // 保存全局实例引用
        window.notionTabManagerInstance = tabManagerElement;

        console.log('NotionTabManager: 全局实例已创建并可用');
    });
}

// 提供便捷的全局API
window.NotionTabs = {
    /**
     * 获取标签页管理器实例
     */
    getInstance() {
        return window.notionTabManagerInstance;
    },

    /**
     * 切换到指定标签页
     */
    switchTo(tabId, options = {}) {
        const instance = this.getInstance();
        return instance ? instance.switchTab(tabId, options) : false;
    },

    /**
     * 获取当前活动标签页
     */
    getActive() {
        const instance = this.getInstance();
        return instance ? instance.getActiveTab() : null;
    },

    /**
     * 获取所有标签页
     */
    getAll() {
        const instance = this.getInstance();
        return instance ? instance.getAllTabs() : [];
    },

    /**
     * 刷新当前标签页
     */
    refresh() {
        const instance = this.getInstance();
        return instance ? instance.refreshCurrentTab() : false;
    }
};

console.log('NotionTabManager: 组件加载完成，支持jQuery兼容模式');
