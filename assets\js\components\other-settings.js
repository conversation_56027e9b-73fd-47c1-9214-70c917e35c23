/**
 * Notion to WordPress - 其他设置组件
 * 处理卸载设置、iframe白名单、图片格式、语言设置和高级性能选项
 */

class NotionOtherSettings extends NotionBaseComponent {
    constructor() {
        super();
        this.state = {
            // 基础设置
            deleteOnUninstall: false,
            iframeWhitelist: '',
            allowedImageTypes: '',
            pluginLanguage: 'auto',
            maxImageSize: 5,
            
            // 高级性能选项
            apiPageSize: 100,
            concurrentRequests: 5,
            batchSize: 20,
            logBufferSize: 50,
            enablePerformanceMode: true,
            
            // 前端资源优化
            enableAssetCompression: true,
            enhancedLazyLoading: true,
            performanceMonitoring: true,
            
            // CDN配置
            enableCdn: false,
            cdnProvider: 'jsdelivr',
            customCdnUrl: '',
            
            isLoading: false,
            hasChanges: false
        };
        
        // 缓存DOM元素引用
        this.elements = {};
    }

    init() {
        // 缓存重要的DOM元素
        this.cacheElements();
        
        // 从现有HTML表单读取数据
        this.loadExistingSettings();
        
        // 绑定事件监听器
        this.bindEvents();
        
        // 渲染组件增强
        this.render();
        
        if (this.config.debugMode) {
            console.log('NotionOtherSettings: 组件初始化完成', this.state);
        }
    }

    /**
     * 缓存DOM元素引用
     */
    cacheElements() {
        this.elements = {
            // 基础设置元素
            deleteOnUninstallCheckbox: document.getElementById('delete_on_uninstall'),
            iframeWhitelistTextarea: document.getElementById('iframe_whitelist'),
            allowedImageTypesTextarea: document.getElementById('allowed_image_types'),
            pluginLanguageSelect: document.getElementById('plugin_language'),
            maxImageSizeInput: document.getElementById('max_image_size'),
            
            // 高级性能选项元素
            apiPageSizeInput: document.getElementById('api_page_size'),
            concurrentRequestsInput: document.getElementById('concurrent_requests'),
            batchSizeInput: document.getElementById('batch_size'),
            logBufferSizeInput: document.getElementById('log_buffer_size'),
            enablePerformanceModeCheckbox: document.getElementById('enable_performance_mode'),
            
            // 前端资源优化元素
            enableAssetCompressionCheckbox: document.getElementById('enable_asset_compression'),
            enhancedLazyLoadingCheckbox: document.getElementById('enhanced_lazy_loading'),
            performanceMonitoringCheckbox: document.getElementById('performance_monitoring'),
            
            // CDN配置元素
            enableCdnCheckbox: document.getElementById('enable_cdn'),
            cdnProviderSelect: document.getElementById('cdn_provider'),
            customCdnUrlInput: document.getElementById('custom_cdn_url'),
            
            // 高级选项容器
            advancedOptionsDetails: document.querySelector('.notion-wp-advanced-options'),
            
            // 其他设置标签页容器
            otherSettingsTab: document.getElementById('other-settings')
        };
    }

    /**
     * 从现有HTML表单读取设置数据
     */
    loadExistingSettings() {
        try {
            const newState = {};
            
            // 基础设置
            if (this.elements.deleteOnUninstallCheckbox) {
                newState.deleteOnUninstall = this.elements.deleteOnUninstallCheckbox.checked;
            }
            if (this.elements.iframeWhitelistTextarea) {
                newState.iframeWhitelist = this.elements.iframeWhitelistTextarea.value || '';
            }
            if (this.elements.allowedImageTypesTextarea) {
                newState.allowedImageTypes = this.elements.allowedImageTypesTextarea.value || '';
            }
            if (this.elements.pluginLanguageSelect) {
                newState.pluginLanguage = this.elements.pluginLanguageSelect.value || 'auto';
            }
            if (this.elements.maxImageSizeInput) {
                newState.maxImageSize = parseInt(this.elements.maxImageSizeInput.value) || 5;
            }
            
            // 高级性能选项
            if (this.elements.apiPageSizeInput) {
                newState.apiPageSize = parseInt(this.elements.apiPageSizeInput.value) || 100;
            }
            if (this.elements.concurrentRequestsInput) {
                newState.concurrentRequests = parseInt(this.elements.concurrentRequestsInput.value) || 5;
            }
            if (this.elements.batchSizeInput) {
                newState.batchSize = parseInt(this.elements.batchSizeInput.value) || 20;
            }
            if (this.elements.logBufferSizeInput) {
                newState.logBufferSize = parseInt(this.elements.logBufferSizeInput.value) || 50;
            }
            if (this.elements.enablePerformanceModeCheckbox) {
                newState.enablePerformanceMode = this.elements.enablePerformanceModeCheckbox.checked;
            }
            
            // 前端资源优化
            if (this.elements.enableAssetCompressionCheckbox) {
                newState.enableAssetCompression = this.elements.enableAssetCompressionCheckbox.checked;
            }
            if (this.elements.enhancedLazyLoadingCheckbox) {
                newState.enhancedLazyLoading = this.elements.enhancedLazyLoadingCheckbox.checked;
            }
            if (this.elements.performanceMonitoringCheckbox) {
                newState.performanceMonitoring = this.elements.performanceMonitoringCheckbox.checked;
            }
            
            // CDN配置
            if (this.elements.enableCdnCheckbox) {
                newState.enableCdn = this.elements.enableCdnCheckbox.checked;
            }
            if (this.elements.cdnProviderSelect) {
                newState.cdnProvider = this.elements.cdnProviderSelect.value || 'jsdelivr';
            }
            if (this.elements.customCdnUrlInput) {
                newState.customCdnUrl = this.elements.customCdnUrlInput.value || '';
            }
            
            this.setState(newState);
            
            if (this.config.debugMode) {
                console.log('NotionOtherSettings: 已加载现有设置数据', newState);
            }
        } catch (error) {
            this.handleError('加载其他设置数据失败', error);
        }
    }

    /**
     * 处理设置项变化
     */
    handleSettingChange(settingKey, value, element = null) {
        // 特殊处理数字类型
        if (['maxImageSize', 'apiPageSize', 'concurrentRequests', 'batchSize', 'logBufferSize'].includes(settingKey)) {
            value = parseInt(value) || 0;
            
            // 验证数字范围
            const ranges = {
                maxImageSize: { min: 1, max: 20 },
                apiPageSize: { min: 50, max: 200 },
                concurrentRequests: { min: 3, max: 15 },
                batchSize: { min: 10, max: 100 },
                logBufferSize: { min: 10, max: 200 }
            };
            
            if (ranges[settingKey]) {
                const { min, max } = ranges[settingKey];
                if (value < min) value = min;
                if (value > max) value = max;
                
                // 更新DOM元素值
                if (element && element.value != value) {
                    element.value = value;
                }
            }
        }
        
        this.setState({ [settingKey]: value, hasChanges: true });
        
        // 处理特殊的联动逻辑
        this.handleSettingInteractions(settingKey, value);
        
        // 广播变化事件
        this.broadcast('other-settings:changed', {
            settingKey,
            value,
            timestamp: Date.now()
        });
        
        if (this.config.debugMode) {
            console.log('NotionOtherSettings: 设置变化', { settingKey, value });
        }
    }

    /**
     * 处理设置项之间的联动逻辑
     */
    handleSettingInteractions(settingKey, value) {
        switch (settingKey) {
            case 'enableCdn':
                // CDN启用状态变化时，控制CDN配置的可见性
                this.updateCdnConfigVisibility(value);
                break;
                
            case 'cdnProvider':
                // CDN提供商变化时，控制自定义URL输入框的可见性
                this.updateCustomCdnUrlVisibility(value === 'custom');
                break;
                
            case 'pluginLanguage':
                // 语言变化时，可以触发界面语言更新（如果需要）
                this.handleLanguageChange(value);
                break;
                
            case 'enablePerformanceMode':
                // 性能模式变化时，可以显示相关提示
                this.showPerformanceModeNotice(value);
                break;
        }
    }

    /**
     * 更新CDN配置的可见性
     */
    updateCdnConfigVisibility(enabled) {
        if (this.elements.cdnProviderSelect) {
            this.elements.cdnProviderSelect.style.display = enabled ? 'block' : 'none';
        }
        
        // 如果CDN被禁用，也隐藏自定义URL输入框
        if (!enabled && this.elements.customCdnUrlInput) {
            this.elements.customCdnUrlInput.style.display = 'none';
        } else if (enabled) {
            this.updateCustomCdnUrlVisibility(this.state.cdnProvider === 'custom');
        }
    }

    /**
     * 更新自定义CDN URL输入框的可见性
     */
    updateCustomCdnUrlVisibility(visible) {
        if (this.elements.customCdnUrlInput) {
            this.elements.customCdnUrlInput.style.display = visible ? 'block' : 'none';
        }
    }

    /**
     * 处理语言变化
     */
    handleLanguageChange(language) {
        // 广播语言变化事件，其他组件可以监听并响应
        this.broadcast('language:changed', {
            language,
            timestamp: Date.now()
        });
        
        if (this.config.debugMode) {
            console.log('NotionOtherSettings: 语言变化', language);
        }
    }

    /**
     * 显示性能模式通知
     */
    showPerformanceModeNotice(enabled) {
        const message = enabled
            ? '性能优化模式已启用，将使用批量操作和并发处理来提升同步速度。'
            : '性能优化模式已禁用，将使用标准的同步处理方式。';

        this.showError(message, enabled ? 'success' : 'info');
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 基础设置事件绑定
        if (this.elements.deleteOnUninstallCheckbox) {
            this.addEventListenerManaged(this.elements.deleteOnUninstallCheckbox, 'change', (e) => {
                this.handleSettingChange('deleteOnUninstall', e.target.checked);
            });
        }

        if (this.elements.iframeWhitelistTextarea) {
            this.addEventListenerManaged(this.elements.iframeWhitelistTextarea, 'input', (e) => {
                this.handleSettingChange('iframeWhitelist', e.target.value);
            });
        }

        if (this.elements.allowedImageTypesTextarea) {
            this.addEventListenerManaged(this.elements.allowedImageTypesTextarea, 'input', (e) => {
                this.handleSettingChange('allowedImageTypes', e.target.value);
            });
        }

        if (this.elements.pluginLanguageSelect) {
            this.addEventListenerManaged(this.elements.pluginLanguageSelect, 'change', (e) => {
                this.handleSettingChange('pluginLanguage', e.target.value);
            });
        }

        if (this.elements.maxImageSizeInput) {
            this.addEventListenerManaged(this.elements.maxImageSizeInput, 'input', (e) => {
                this.handleSettingChange('maxImageSize', e.target.value, e.target);
            });
        }

        // 高级性能选项事件绑定
        if (this.elements.apiPageSizeInput) {
            this.addEventListenerManaged(this.elements.apiPageSizeInput, 'input', (e) => {
                this.handleSettingChange('apiPageSize', e.target.value, e.target);
            });
        }

        if (this.elements.concurrentRequestsInput) {
            this.addEventListenerManaged(this.elements.concurrentRequestsInput, 'input', (e) => {
                this.handleSettingChange('concurrentRequests', e.target.value, e.target);
            });
        }

        if (this.elements.batchSizeInput) {
            this.addEventListenerManaged(this.elements.batchSizeInput, 'input', (e) => {
                this.handleSettingChange('batchSize', e.target.value, e.target);
            });
        }

        if (this.elements.logBufferSizeInput) {
            this.addEventListenerManaged(this.elements.logBufferSizeInput, 'input', (e) => {
                this.handleSettingChange('logBufferSize', e.target.value, e.target);
            });
        }

        if (this.elements.enablePerformanceModeCheckbox) {
            this.addEventListenerManaged(this.elements.enablePerformanceModeCheckbox, 'change', (e) => {
                this.handleSettingChange('enablePerformanceMode', e.target.checked);
            });
        }

        // 前端资源优化事件绑定
        if (this.elements.enableAssetCompressionCheckbox) {
            this.addEventListenerManaged(this.elements.enableAssetCompressionCheckbox, 'change', (e) => {
                this.handleSettingChange('enableAssetCompression', e.target.checked);
            });
        }

        if (this.elements.enhancedLazyLoadingCheckbox) {
            this.addEventListenerManaged(this.elements.enhancedLazyLoadingCheckbox, 'change', (e) => {
                this.handleSettingChange('enhancedLazyLoading', e.target.checked);
            });
        }

        if (this.elements.performanceMonitoringCheckbox) {
            this.addEventListenerManaged(this.elements.performanceMonitoringCheckbox, 'change', (e) => {
                this.handleSettingChange('performanceMonitoring', e.target.checked);
            });
        }

        // CDN配置事件绑定
        if (this.elements.enableCdnCheckbox) {
            this.addEventListenerManaged(this.elements.enableCdnCheckbox, 'change', (e) => {
                this.handleSettingChange('enableCdn', e.target.checked);
            });
        }

        if (this.elements.cdnProviderSelect) {
            this.addEventListenerManaged(this.elements.cdnProviderSelect, 'change', (e) => {
                this.handleSettingChange('cdnProvider', e.target.value);
            });
        }

        if (this.elements.customCdnUrlInput) {
            this.addEventListenerManaged(this.elements.customCdnUrlInput, 'input', (e) => {
                this.handleSettingChange('customCdnUrl', e.target.value);
            });
        }

        // 监听标签页切换事件
        this.subscribe('tab:changed', (data) => {
            if (data.tabId === 'other-settings') {
                // 切换到其他设置标签页时刷新数据
                setTimeout(() => {
                    this.loadExistingSettings();
                    this.updateAllInteractions();
                }, 100);
            }
        });

        if (this.config.debugMode) {
            console.log('NotionOtherSettings: 事件绑定完成');
        }
    }

    /**
     * 更新所有设置项的联动效果
     */
    updateAllInteractions() {
        // 更新CDN配置可见性
        this.updateCdnConfigVisibility(this.state.enableCdn);

        // 更新自定义CDN URL可见性
        this.updateCustomCdnUrlVisibility(this.state.cdnProvider === 'custom');
    }

    /**
     * 渲染组件（增强现有HTML结构）
     */
    render() {
        // 在jQuery兼容模式下，不渲染自己的HTML
        // 而是增强现有的HTML结构
        if (!this.elements.otherSettingsTab) {
            console.warn('NotionOtherSettings: 未找到现有的其他设置表单结构');
            return;
        }

        // 添加组件标识
        this.elements.otherSettingsTab.setAttribute('data-enhanced-by', 'notion-other-settings');

        // 初始化所有联动效果
        this.updateAllInteractions();

        if (this.config.debugMode) {
            console.log('NotionOtherSettings: HTML结构增强完成');
        }
    }

    /**
     * 验证设置配置
     */
    validateSettings() {
        const errors = [];
        const warnings = [];

        // 验证iframe白名单格式
        if (this.state.iframeWhitelist && this.state.iframeWhitelist !== '*') {
            const domains = this.state.iframeWhitelist.split(',').map(d => d.trim());
            const invalidDomains = domains.filter(domain => {
                // 简单的域名格式验证
                return domain && !/^[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(domain);
            });

            if (invalidDomains.length > 0) {
                warnings.push(`iframe白名单中包含可能无效的域名：${invalidDomains.join(', ')}`);
            }
        }

        // 验证图片格式
        if (this.state.allowedImageTypes && this.state.allowedImageTypes !== '*') {
            const types = this.state.allowedImageTypes.split(',').map(t => t.trim());
            const invalidTypes = types.filter(type => {
                return type && !type.startsWith('image/');
            });

            if (invalidTypes.length > 0) {
                warnings.push(`图片格式中包含可能无效的MIME类型：${invalidTypes.join(', ')}`);
            }
        }

        // 验证自定义CDN URL
        if (this.state.enableCdn && this.state.cdnProvider === 'custom' && this.state.customCdnUrl) {
            try {
                new URL(this.state.customCdnUrl);
            } catch (e) {
                errors.push('自定义CDN URL格式无效');
            }
        }

        // 验证数字范围
        const numberValidations = [
            { key: 'maxImageSize', min: 1, max: 20, name: '最大图片大小' },
            { key: 'apiPageSize', min: 50, max: 200, name: 'API分页大小' },
            { key: 'concurrentRequests', min: 3, max: 15, name: '并发请求数' },
            { key: 'batchSize', min: 10, max: 100, name: '批量处理大小' },
            { key: 'logBufferSize', min: 10, max: 200, name: '日志缓冲大小' }
        ];

        numberValidations.forEach(({ key, min, max, name }) => {
            const value = this.state[key];
            if (value < min || value > max) {
                errors.push(`${name}必须在${min}-${max}之间`);
            }
        });

        return {
            isValid: errors.length === 0,
            errors,
            warnings
        };
    }

    /**
     * 获取当前设置配置
     */
    getCurrentSettings() {
        return {
            basic: {
                deleteOnUninstall: this.state.deleteOnUninstall,
                iframeWhitelist: this.state.iframeWhitelist,
                allowedImageTypes: this.state.allowedImageTypes,
                pluginLanguage: this.state.pluginLanguage,
                maxImageSize: this.state.maxImageSize
            },
            advanced: {
                apiPageSize: this.state.apiPageSize,
                concurrentRequests: this.state.concurrentRequests,
                batchSize: this.state.batchSize,
                logBufferSize: this.state.logBufferSize,
                enablePerformanceMode: this.state.enablePerformanceMode
            },
            frontend: {
                enableAssetCompression: this.state.enableAssetCompression,
                enhancedLazyLoading: this.state.enhancedLazyLoading,
                performanceMonitoring: this.state.performanceMonitoring
            },
            cdn: {
                enableCdn: this.state.enableCdn,
                cdnProvider: this.state.cdnProvider,
                customCdnUrl: this.state.customCdnUrl
            },
            hasChanges: this.state.hasChanges
        };
    }

    /**
     * 重置设置配置
     */
    resetSettings() {
        this.loadExistingSettings();
        this.setState({ hasChanges: false });
        this.updateAllInteractions();

        // 广播重置事件
        this.broadcast('other-settings:reset', {
            timestamp: Date.now()
        });

        if (this.config.debugMode) {
            console.log('NotionOtherSettings: 设置配置已重置');
        }
    }

    /**
     * 应用推荐设置
     */
    applyRecommendedSettings() {
        const recommendedSettings = {
            maxImageSize: 5,
            apiPageSize: 100,
            concurrentRequests: 5,
            batchSize: 20,
            logBufferSize: 50,
            enablePerformanceMode: true,
            enableAssetCompression: true,
            enhancedLazyLoading: true,
            performanceMonitoring: true,
            enableCdn: false
        };

        // 应用推荐设置到状态
        this.setState({ ...recommendedSettings, hasChanges: true });

        // 更新DOM中的输入值
        Object.entries(recommendedSettings).forEach(([key, value]) => {
            const element = this.getElementBySettingKey(key);
            if (element) {
                if (element.type === 'checkbox') {
                    element.checked = value;
                } else {
                    element.value = value;
                }
            }
        });

        // 更新联动效果
        this.updateAllInteractions();

        // 广播推荐设置应用事件
        this.broadcast('other-settings:recommended-applied', {
            settings: recommendedSettings,
            timestamp: Date.now()
        });

        this.showError('已应用推荐设置配置', 'success');

        if (this.config.debugMode) {
            console.log('NotionOtherSettings: 应用推荐设置', recommendedSettings);
        }
    }

    /**
     * 根据设置键获取对应的DOM元素
     */
    getElementBySettingKey(key) {
        const elementMap = {
            deleteOnUninstall: this.elements.deleteOnUninstallCheckbox,
            iframeWhitelist: this.elements.iframeWhitelistTextarea,
            allowedImageTypes: this.elements.allowedImageTypesTextarea,
            pluginLanguage: this.elements.pluginLanguageSelect,
            maxImageSize: this.elements.maxImageSizeInput,
            apiPageSize: this.elements.apiPageSizeInput,
            concurrentRequests: this.elements.concurrentRequestsInput,
            batchSize: this.elements.batchSizeInput,
            logBufferSize: this.elements.logBufferSizeInput,
            enablePerformanceMode: this.elements.enablePerformanceModeCheckbox,
            enableAssetCompression: this.elements.enableAssetCompressionCheckbox,
            enhancedLazyLoading: this.elements.enhancedLazyLoadingCheckbox,
            performanceMonitoring: this.elements.performanceMonitoringCheckbox,
            enableCdn: this.elements.enableCdnCheckbox,
            cdnProvider: this.elements.cdnProviderSelect,
            customCdnUrl: this.elements.customCdnUrlInput
        };

        return elementMap[key] || null;
    }

    /**
     * 导出设置配置
     */
    exportSettings() {
        const settings = this.getCurrentSettings();
        const exportData = {
            version: '2.0.0',
            timestamp: new Date().toISOString(),
            settings: settings
        };

        return JSON.stringify(exportData, null, 2);
    }

    /**
     * 导入设置配置
     */
    importSettings(jsonData) {
        try {
            const importData = JSON.parse(jsonData);

            if (!importData.settings) {
                throw new Error('无效的设置数据格式');
            }

            const { basic, advanced, frontend, cdn } = importData.settings;

            // 合并所有设置
            const newSettings = {
                ...basic,
                ...advanced,
                ...frontend,
                ...cdn,
                hasChanges: true
            };

            // 应用设置到状态
            this.setState(newSettings);

            // 更新DOM元素
            Object.entries(newSettings).forEach(([key, value]) => {
                if (key !== 'hasChanges') {
                    const element = this.getElementBySettingKey(key);
                    if (element) {
                        if (element.type === 'checkbox') {
                            element.checked = value;
                        } else {
                            element.value = value;
                        }
                    }
                }
            });

            // 更新联动效果
            this.updateAllInteractions();

            // 广播导入事件
            this.broadcast('other-settings:imported', {
                settings: newSettings,
                timestamp: Date.now()
            });

            this.showError('设置配置导入成功', 'success');

            if (this.config.debugMode) {
                console.log('NotionOtherSettings: 设置导入成功', newSettings);
            }

            return true;
        } catch (error) {
            this.handleError('导入设置配置失败', error);
            return false;
        }
    }

    /**
     * 组件销毁时的清理
     */
    onBeforeUnmount() {
        if (this.config.debugMode) {
            console.log('NotionOtherSettings: 组件清理完成');
        }
    }
}

// ==================== 组件注册和导出 ====================

// 注册Web Component
if (!customElements.get('notion-other-settings')) {
    customElements.define('notion-other-settings', NotionOtherSettings);
}

// 注册到Notion组件注册表
if (window.NotionRegistry) {
    window.NotionRegistry.register('notion-other-settings', NotionOtherSettings);
} else {
    console.warn('NotionOtherSettings: NotionRegistry未找到，使用标准Web Components注册');
}

// 导出到全局作用域
window.NotionOtherSettings = NotionOtherSettings;

// 创建全局实例（单例模式）
if (!window.notionOtherSettingsInstance) {
    // 延迟创建实例，确保DOM已加载
    document.addEventListener('DOMContentLoaded', () => {
        // 查找其他设置容器
        const otherSettingsContainer = document.getElementById('other-settings');

        if (otherSettingsContainer) {
            // 创建组件实例
            const otherSettingsComponent = document.createElement('notion-other-settings');
            otherSettingsComponent.style.display = 'none'; // 隐藏，因为我们只是用来增强现有结构

            // 插入到容器中
            otherSettingsContainer.appendChild(otherSettingsComponent);

            // 保存全局实例引用
            window.notionOtherSettingsInstance = otherSettingsComponent;

            console.log('NotionOtherSettings: 全局实例已创建并可用');
        } else {
            console.warn('NotionOtherSettings: 未找到其他设置容器');
        }
    });
}

// 提供便捷的全局API
window.NotionOtherSettingsAPI = {
    /**
     * 获取其他设置组件实例
     */
    getInstance() {
        return window.notionOtherSettingsInstance;
    },

    /**
     * 获取当前设置配置
     */
    getCurrentSettings() {
        const instance = this.getInstance();
        return instance ? instance.getCurrentSettings() : null;
    },

    /**
     * 验证设置配置
     */
    validateSettings() {
        const instance = this.getInstance();
        return instance ? instance.validateSettings() : { isValid: false, errors: ['组件未初始化'], warnings: [] };
    },

    /**
     * 重置设置配置
     */
    resetSettings() {
        const instance = this.getInstance();
        return instance ? instance.resetSettings() : false;
    },

    /**
     * 应用推荐设置
     */
    applyRecommendedSettings() {
        const instance = this.getInstance();
        return instance ? instance.applyRecommendedSettings() : false;
    },

    /**
     * 导出设置配置
     */
    exportSettings() {
        const instance = this.getInstance();
        return instance ? instance.exportSettings() : null;
    },

    /**
     * 导入设置配置
     */
    importSettings(jsonData) {
        const instance = this.getInstance();
        return instance ? instance.importSettings(jsonData) : false;
    },

    /**
     * 获取基础设置
     */
    getBasicSettings() {
        const settings = this.getCurrentSettings();
        return settings ? settings.basic : null;
    },

    /**
     * 获取高级设置
     */
    getAdvancedSettings() {
        const settings = this.getCurrentSettings();
        return settings ? settings.advanced : null;
    },

    /**
     * 获取前端优化设置
     */
    getFrontendSettings() {
        const settings = this.getCurrentSettings();
        return settings ? settings.frontend : null;
    },

    /**
     * 获取CDN设置
     */
    getCdnSettings() {
        const settings = this.getCurrentSettings();
        return settings ? settings.cdn : null;
    },

    /**
     * 检查是否有未保存的更改
     */
    hasUnsavedChanges() {
        const settings = this.getCurrentSettings();
        return settings ? settings.hasChanges : false;
    },

    /**
     * 更新特定设置项
     */
    updateSetting(key, value) {
        const instance = this.getInstance();
        if (instance) {
            instance.handleSettingChange(key, value);
            return true;
        }
        return false;
    }
};

console.log('NotionOtherSettings: 组件加载完成，支持基础设置、高级性能选项、前端优化和CDN配置管理');
