/**
 * Notion to WordPress - 组件注册系统
 * 管理所有Web Components的注册和生命周期
 */

class NotionComponentRegistry {
    constructor() {
        this.components = new Map();
        this.initialized = false;
    }

    /**
     * 注册组件
     */
    register(name, componentClass, options = {}) {
        if (this.components.has(name)) {
            console.warn(`Component ${name} is already registered`);
            return;
        }

        // 验证组件类
        if (!componentClass || typeof componentClass !== 'function') {
            throw new Error(`Invalid component class for ${name}`);
        }

        // 注册Web Component
        if (!customElements.get(name)) {
            customElements.define(name, componentClass);
        }

        // 存储组件信息
        this.components.set(name, {
            class: componentClass,
            options,
            instances: new Set()
        });

        console.log(`Component ${name} registered successfully`);
    }

    /**
     * 获取组件信息
     */
    get(name) {
        return this.components.get(name);
    }

    /**
     * 检查组件是否已注册
     */
    has(name) {
        return this.components.has(name);
    }

    /**
     * 创建组件实例
     */
    create(name, attributes = {}) {
        const componentInfo = this.components.get(name);
        if (!componentInfo) {
            throw new Error(`Component ${name} is not registered`);
        }

        const element = document.createElement(name);
        
        // 设置属性
        Object.entries(attributes).forEach(([key, value]) => {
            element.setAttribute(key, value);
        });

        // 跟踪实例
        componentInfo.instances.add(element);

        return element;
    }

    /**
     * 销毁组件实例
     */
    destroy(element) {
        if (!element || !element.tagName) return;

        const name = element.tagName.toLowerCase();
        const componentInfo = this.components.get(name);
        
        if (componentInfo) {
            componentInfo.instances.delete(element);
        }

        // 从DOM中移除
        if (element.parentElement) {
            element.parentElement.removeChild(element);
        }
    }

    /**
     * 获取所有组件实例
     */
    getInstances(name) {
        const componentInfo = this.components.get(name);
        return componentInfo ? Array.from(componentInfo.instances) : [];
    }

    /**
     * 初始化所有组件
     */
    init() {
        if (this.initialized) return;

        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => this.init());
            return;
        }

        // 扫描并初始化页面中的组件
        this.scanAndInitialize();
        
        // 监听动态添加的组件
        this.observeChanges();
        
        this.initialized = true;
        console.log('Notion Component Registry initialized');
    }

    /**
     * 扫描并初始化页面中的组件
     */
    scanAndInitialize() {
        this.components.forEach((componentInfo, name) => {
            const elements = document.querySelectorAll(name);
            elements.forEach(element => {
                if (!componentInfo.instances.has(element)) {
                    componentInfo.instances.add(element);
                }
            });
        });
    }

    /**
     * 监听DOM变化
     */
    observeChanges() {
        const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                // 处理新增的节点
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.handleAddedNode(node);
                    }
                });

                // 处理移除的节点
                mutation.removedNodes.forEach((node) => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.handleRemovedNode(node);
                    }
                });
            });
        });

        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

    /**
     * 处理新增的节点
     */
    handleAddedNode(node) {
        const tagName = node.tagName?.toLowerCase();
        if (tagName && this.components.has(tagName)) {
            const componentInfo = this.components.get(tagName);
            componentInfo.instances.add(node);
        }

        // 检查子节点
        if (node.querySelectorAll) {
            this.components.forEach((componentInfo, name) => {
                const childElements = node.querySelectorAll(name);
                childElements.forEach(element => {
                    componentInfo.instances.add(element);
                });
            });
        }
    }

    /**
     * 处理移除的节点
     */
    handleRemovedNode(node) {
        const tagName = node.tagName?.toLowerCase();
        if (tagName && this.components.has(tagName)) {
            const componentInfo = this.components.get(tagName);
            componentInfo.instances.delete(node);
        }

        // 检查子节点
        if (node.querySelectorAll) {
            this.components.forEach((componentInfo, name) => {
                const childElements = node.querySelectorAll(name);
                childElements.forEach(element => {
                    componentInfo.instances.delete(element);
                });
            });
        }
    }

    /**
     * 获取注册统计信息
     */
    getStats() {
        const stats = {
            totalComponents: this.components.size,
            totalInstances: 0,
            components: {}
        };

        this.components.forEach((componentInfo, name) => {
            const instanceCount = componentInfo.instances.size;
            stats.totalInstances += instanceCount;
            stats.components[name] = {
                instances: instanceCount,
                class: componentInfo.class.name
            };
        });

        return stats;
    }

    /**
     * 清理所有组件
     */
    cleanup() {
        this.components.forEach((componentInfo, name) => {
            componentInfo.instances.forEach(instance => {
                if (instance.cleanup && typeof instance.cleanup === 'function') {
                    instance.cleanup();
                }
            });
            componentInfo.instances.clear();
        });
        
        this.components.clear();
        this.initialized = false;
    }
}

// 创建全局注册器实例
window.NotionRegistry = new NotionComponentRegistry();

// 自动初始化
window.NotionRegistry.init();
