/**
 * Notion to WordPress - 调试工具组件
 * 处理日志级别设置、日志保留期设置和日志查看器功能
 */

class NotionDebugTools extends NotionBaseComponent {
    constructor() {
        super();
        this.state = {
            // 调试设置
            settings: {
                debugLevel: 'error',
                logRetentionDays: '7'
            },
            // 日志数据
            logs: [],
            logFiles: [],
            selectedLogFile: '',
            // 状态管理
            isLoading: false,
            isLoadingLogs: false,
            error: null,
            lastRefresh: null
        };
        
        // 缓存DOM元素引用
        this.elements = {};
    }

    init() {
        // 缓存DOM元素
        this.cacheElements();
        
        // 加载调试设置
        this.loadDebugSettings();
        
        // 初始化日志查看器
        this.initLogViewer();
        
        // 绑定事件监听器
        this.bindEvents();
        
        // 渲染组件增强
        this.render();
        
        if (this.config.debugMode) {
            console.log('NotionDebugTools: 组件初始化完成', this.state);
        }
    }

    /**
     * 缓存DOM元素引用
     */
    cacheElements() {
        this.elements = {
            // 调试工具标签页容器
            debugTab: document.getElementById('debug'),
            
            // 设置元素
            debugLevelSelect: document.getElementById('debug_level'),
            logRetentionSelect: document.getElementById('log_retention_days'),
            
            // 日志查看器元素
            logViewerContainer: document.getElementById('log-viewer-container'),
            logFileSelector: document.getElementById('log-file-selector'),
            viewLogButton: document.getElementById('view-log-button'),
            clearLogsButton: document.getElementById('clear-logs-button'),
            logViewer: document.getElementById('log-viewer')
        };
    }

    /**
     * 加载调试设置
     */
    loadDebugSettings() {
        try {
            const settings = {
                debugLevel: this.elements.debugLevelSelect?.value || 'error',
                logRetentionDays: this.elements.logRetentionSelect?.value || '7'
            };

            this.setState({ settings });
            
            // 加载日志文件列表
            this.loadLogFiles();
            
            if (this.config.debugMode) {
                console.log('NotionDebugTools: 已加载调试设置', settings);
            }
        } catch (error) {
            this.handleError('加载调试设置失败', error);
        }
    }

    /**
     * 加载日志文件列表
     */
    loadLogFiles() {
        const logFiles = [];
        const selector = this.elements.logFileSelector;
        
        if (selector) {
            Array.from(selector.options).forEach(option => {
                logFiles.push(option.value);
            });
            
            this.setState({ 
                logFiles,
                selectedLogFile: selector.value || logFiles[0] || ''
            });
        }
    }

    /**
     * 初始化日志查看器
     */
    async initLogViewer() {
        if (!this.state.selectedLogFile) {
            return;
        }
        
        try {
            await this.loadLogContent();
        } catch (error) {
            this.handleError('初始化日志查看器失败', error);
        }
    }

    /**
     * 加载日志内容
     */
    async loadLogContent() {
        if (!this.state.selectedLogFile) {
            this.displayLogs([]);
            return;
        }
        
        this.setState({ isLoadingLogs: true, error: null });

        try {
            const response = this.services.api 
                ? await this.services.api.request('notion_to_wordpress_view_log', {
                    log_file: this.state.selectedLogFile
                })
                : await this.request(window.notionToWp.ajax_url, {
                    body: new URLSearchParams({
                        action: 'notion_to_wordpress_view_log',
                        log_file: this.state.selectedLogFile,
                        nonce: window.notionToWp.nonce
                    })
                });

            if (response.success) {
                const logs = this.parseLogContent(response.data.content || '');
                this.setState({ 
                    logs,
                    isLoadingLogs: false,
                    lastRefresh: new Date().toLocaleString()
                });
                this.displayLogs(logs);
            } else {
                throw new Error(response.data?.message || '加载日志失败');
            }
        } catch (error) {
            console.error('Failed to load log content:', error);
            this.setState({
                isLoadingLogs: false,
                error: '加载日志内容失败: ' + error.message
            });
            this.displayLogs([]);
        }
    }

    /**
     * 解析日志内容
     */
    parseLogContent(content) {
        if (!content || typeof content !== 'string') {
            return [];
        }

        const lines = content.split('\n').filter(line => line.trim());
        const logs = [];

        lines.forEach(line => {
            // 尝试解析日志格式：[时间] [级别] 消息
            const match = line.match(/^\[([^\]]+)\]\s*\[([^\]]+)\]\s*(.+)$/);
            if (match) {
                logs.push({
                    timestamp: match[1],
                    level: match[2].toLowerCase(),
                    message: match[3]
                });
            } else {
                // 如果不匹配标准格式，作为普通消息处理
                logs.push({
                    timestamp: new Date().toLocaleString(),
                    level: 'info',
                    message: line
                });
            }
        });

        return logs.reverse(); // 最新的日志在前
    }

    /**
     * 显示日志内容
     */
    displayLogs(logs) {
        const logViewer = this.elements.logViewer;
        if (!logViewer) return;

        if (logs.length === 0) {
            logViewer.value = '暂无日志内容';
            return;
        }

        const logContent = logs.map(log => {
            const levelTag = `[${log.level.toUpperCase()}]`;
            return `[${log.timestamp}] ${levelTag} ${log.message}`;
        }).join('\n');

        logViewer.value = logContent;
        
        // 滚动到底部显示最新日志
        logViewer.scrollTop = logViewer.scrollHeight;
    }

    /**
     * 清除所有日志
     */
    async clearLogs() {
        if (!confirm('确定要清除所有日志吗？此操作不可撤销。')) {
            return;
        }
        
        try {
            this.setState({ isLoading: true });
            
            const response = this.services.api 
                ? await this.services.api.request('notion_to_wordpress_clear_logs')
                : await this.request(window.notionToWp.ajax_url, {
                    body: new URLSearchParams({
                        action: 'notion_to_wordpress_clear_logs',
                        nonce: window.notionToWp.nonce
                    })
                });
            
            if (response.success) {
                this.showSuccess('所有日志已清除');
                await this.loadLogContent(); // 重新加载日志
            } else {
                throw new Error(response.data?.message || '清除日志失败');
            }
        } catch (error) {
            this.handleError('清除日志失败', error);
        } finally {
            this.setState({ isLoading: false });
        }
    }

    /**
     * 保存调试设置
     */
    async saveDebugSettings() {
        try {
            this.setState({ isLoading: true });
            
            const settings = {
                debug_level: this.elements.debugLevelSelect?.value || 'error',
                log_retention_days: this.elements.logRetentionSelect?.value || '7'
            };
            
            const response = this.services.api 
                ? await this.services.api.request('notion_to_wordpress_save_debug_settings', settings)
                : await this.request(window.notionToWp.ajax_url, {
                    body: new URLSearchParams({
                        action: 'notion_to_wordpress_save_debug_settings',
                        ...settings,
                        nonce: window.notionToWp.nonce
                    })
                });
            
            if (response.success) {
                this.setState({ 
                    settings: {
                        debugLevel: settings.debug_level,
                        logRetentionDays: settings.log_retention_days
                    }
                });
                this.showSuccess('调试设置已保存');
            } else {
                throw new Error(response.data?.message || '保存设置失败');
            }
        } catch (error) {
            this.handleError('保存调试设置失败', error);
        } finally {
            this.setState({ isLoading: false });
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 查看日志按钮
        if (this.elements.viewLogButton) {
            this.addEventListenerManaged(this.elements.viewLogButton, 'click', () => {
                this.loadLogContent();
            });
        }
        
        // 清除日志按钮
        if (this.elements.clearLogsButton) {
            this.addEventListenerManaged(this.elements.clearLogsButton, 'click', () => {
                this.clearLogs();
            });
        }
        
        // 日志文件选择器
        if (this.elements.logFileSelector) {
            this.addEventListenerManaged(this.elements.logFileSelector, 'change', (e) => {
                this.setState({ selectedLogFile: e.target.value });
                this.loadLogContent();
            });
        }
        
        // 调试级别变更
        if (this.elements.debugLevelSelect) {
            this.addEventListenerManaged(this.elements.debugLevelSelect, 'change', () => {
                this.saveDebugSettings();
            });
        }
        
        // 日志保留期变更
        if (this.elements.logRetentionSelect) {
            this.addEventListenerManaged(this.elements.logRetentionSelect, 'change', () => {
                this.saveDebugSettings();
            });
        }
        
        // 标签页切换监听
        this.bindTabSwitchListener();
    }

    /**
     * 绑定标签页切换监听
     */
    bindTabSwitchListener() {
        // 监听标签页切换事件
        this.subscribe('tab:switched', (data) => {
            if (data.tabId === 'debug') {
                // 当切换到调试工具标签页时，刷新日志内容
                this.loadLogContent();
            }
        });

        // 监听窗口可见性变化
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden && this.isVisible()) {
                this.loadLogContent();
            }
        });
    }

    /**
     * 检查组件是否可见
     */
    isVisible() {
        const debugTab = this.elements.debugTab;
        if (!debugTab) return false;

        // 检查标签页是否激活
        return debugTab.classList.contains('active') ||
               debugTab.style.display !== 'none';
    }

    /**
     * 渲染组件增强
     */
    render() {
        // 如果组件容器不存在，创建增强显示
        if (!this.elements.logViewerContainer) {
            this.createEnhancedLogViewer();
        }

        // 更新加载状态显示
        this.updateLoadingDisplay();

        // 更新错误显示
        this.updateErrorDisplay();
    }

    /**
     * 创建增强的日志查看器
     */
    createEnhancedLogViewer() {
        const debugTab = this.elements.debugTab;
        if (!debugTab) return;

        // 查找现有的日志查看器容器
        let container = debugTab.querySelector('#log-viewer-container');
        if (!container) {
            // 如果不存在，创建一个基本的容器
            container = document.createElement('div');
            container.id = 'log-viewer-container';
            container.className = 'notion-debug-log-viewer';

            // 插入到调试工具区域
            const settingsSection = debugTab.querySelector('.notion-wp-settings-section');
            if (settingsSection) {
                settingsSection.appendChild(container);
            }
        }

        // 添加增强功能
        this.enhanceLogViewer(container);
    }

    /**
     * 增强日志查看器
     */
    enhanceLogViewer(container) {
        // 添加工具栏
        let toolbar = container.querySelector('.log-viewer-toolbar');
        if (!toolbar) {
            toolbar = document.createElement('div');
            toolbar.className = 'log-viewer-toolbar';
            toolbar.innerHTML = `
                <div class="log-viewer-controls">
                    <button type="button" class="button button-secondary" id="refresh-logs-button">
                        <span class="dashicons dashicons-update"></span>
                        刷新日志
                    </button>
                    <button type="button" class="button button-secondary" id="download-logs-button">
                        <span class="dashicons dashicons-download"></span>
                        下载日志
                    </button>
                    <span class="log-viewer-status">
                        ${this.state.lastRefresh ? `最后更新: ${this.state.lastRefresh}` : ''}
                    </span>
                </div>
                <div class="log-viewer-filters">
                    <label for="log-level-filter">级别过滤:</label>
                    <select id="log-level-filter">
                        <option value="">全部</option>
                        <option value="error">错误</option>
                        <option value="warning">警告</option>
                        <option value="info">信息</option>
                        <option value="debug">调试</option>
                    </select>
                    <label for="log-search">搜索:</label>
                    <input type="text" id="log-search" placeholder="搜索日志内容..." />
                </div>
            `;
            container.insertBefore(toolbar, container.firstChild);

            // 绑定工具栏事件
            this.bindToolbarEvents(toolbar);
        }

        // 添加日志统计信息
        this.updateLogStats(container);
    }

    /**
     * 绑定工具栏事件
     */
    bindToolbarEvents(toolbar) {
        // 刷新日志按钮
        const refreshBtn = toolbar.querySelector('#refresh-logs-button');
        if (refreshBtn) {
            this.addEventListenerManaged(refreshBtn, 'click', () => {
                this.loadLogContent();
            });
        }

        // 下载日志按钮
        const downloadBtn = toolbar.querySelector('#download-logs-button');
        if (downloadBtn) {
            this.addEventListenerManaged(downloadBtn, 'click', () => {
                this.downloadLogs();
            });
        }

        // 级别过滤
        const levelFilter = toolbar.querySelector('#log-level-filter');
        if (levelFilter) {
            this.addEventListenerManaged(levelFilter, 'change', (e) => {
                this.filterLogs({ level: e.target.value });
            });
        }

        // 搜索过滤
        const searchInput = toolbar.querySelector('#log-search');
        if (searchInput) {
            this.addEventListenerManaged(searchInput, 'input', (e) => {
                this.filterLogs({ search: e.target.value });
            });
        }
    }

    /**
     * 过滤日志
     */
    filterLogs(filters) {
        let filteredLogs = [...this.state.logs];

        // 级别过滤
        if (filters.level) {
            filteredLogs = filteredLogs.filter(log => log.level === filters.level);
        }

        // 搜索过滤
        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            filteredLogs = filteredLogs.filter(log =>
                log.message.toLowerCase().includes(searchTerm) ||
                log.timestamp.toLowerCase().includes(searchTerm)
            );
        }

        this.displayLogs(filteredLogs);
        this.updateLogStats(this.elements.logViewerContainer, filteredLogs);
    }

    /**
     * 下载日志
     */
    downloadLogs() {
        if (this.state.logs.length === 0) {
            this.showError('没有可下载的日志内容');
            return;
        }

        const logContent = this.state.logs.map(log => {
            return `[${log.timestamp}] [${log.level.toUpperCase()}] ${log.message}`;
        }).join('\n');

        const blob = new Blob([logContent], { type: 'text/plain' });
        const url = URL.createObjectURL(blob);

        const a = document.createElement('a');
        a.href = url;
        a.download = `notion-wp-logs-${this.state.selectedLogFile}-${new Date().toISOString().split('T')[0]}.txt`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);

        URL.revokeObjectURL(url);
        this.showSuccess('日志文件已下载');
    }

    /**
     * 更新日志统计信息
     */
    updateLogStats(container, logs = null) {
        if (!container) return;

        const logsToCount = logs || this.state.logs;
        const stats = {
            total: logsToCount.length,
            error: logsToCount.filter(log => log.level === 'error').length,
            warning: logsToCount.filter(log => log.level === 'warning').length,
            info: logsToCount.filter(log => log.level === 'info').length,
            debug: logsToCount.filter(log => log.level === 'debug').length
        };

        let statsContainer = container.querySelector('.log-stats');
        if (!statsContainer) {
            statsContainer = document.createElement('div');
            statsContainer.className = 'log-stats';
            container.appendChild(statsContainer);
        }

        statsContainer.innerHTML = `
            <div class="log-stats-item">
                <span class="stats-label">总计:</span>
                <span class="stats-value">${stats.total}</span>
            </div>
            <div class="log-stats-item error">
                <span class="stats-label">错误:</span>
                <span class="stats-value">${stats.error}</span>
            </div>
            <div class="log-stats-item warning">
                <span class="stats-label">警告:</span>
                <span class="stats-value">${stats.warning}</span>
            </div>
            <div class="log-stats-item info">
                <span class="stats-label">信息:</span>
                <span class="stats-value">${stats.info}</span>
            </div>
            <div class="log-stats-item debug">
                <span class="stats-label">调试:</span>
                <span class="stats-value">${stats.debug}</span>
            </div>
        `;
    }

    /**
     * 更新加载状态显示
     */
    updateLoadingDisplay() {
        const buttons = [
            this.elements.viewLogButton,
            this.elements.clearLogsButton,
            this.querySelector('#refresh-logs-button')
        ].filter(Boolean);

        buttons.forEach(button => {
            if (this.state.isLoading || this.state.isLoadingLogs) {
                button.disabled = true;
                button.style.opacity = '0.6';
            } else {
                button.disabled = false;
                button.style.opacity = '1';
            }
        });

        // 更新日志查看器状态
        if (this.elements.logViewer && this.state.isLoadingLogs) {
            this.elements.logViewer.value = '正在加载日志...';
        }
    }

    /**
     * 更新错误显示
     */
    updateErrorDisplay() {
        let errorContainer = this.querySelector('.debug-error');

        if (this.state.error) {
            if (!errorContainer) {
                errorContainer = document.createElement('div');
                errorContainer.className = 'debug-error notice notice-error';
                this.elements.debugTab?.querySelector('.notion-wp-settings-section')?.appendChild(errorContainer);
            }

            errorContainer.innerHTML = `
                <p><strong>错误:</strong> ${this.escapeHtml(this.state.error)}</p>
                <button type="button" class="notice-dismiss" onclick="this.parentElement.style.display='none'">
                    <span class="screen-reader-text">忽略此通知</span>
                </button>
            `;
        } else if (errorContainer) {
            errorContainer.remove();
        }
    }

    /**
     * 显示成功消息
     */
    showSuccess(message) {
        this.setState({ error: null });

        // 创建临时成功提示
        const successDiv = document.createElement('div');
        successDiv.className = 'debug-success notice notice-success is-dismissible';
        successDiv.innerHTML = `
            <p><strong>成功:</strong> ${this.escapeHtml(message)}</p>
            <button type="button" class="notice-dismiss">
                <span class="screen-reader-text">忽略此通知</span>
            </button>
        `;

        // 插入到调试工具区域顶部
        const settingsSection = this.elements.debugTab?.querySelector('.notion-wp-settings-section');
        if (settingsSection) {
            settingsSection.insertBefore(successDiv, settingsSection.firstChild);
        }

        // 3秒后自动移除
        setTimeout(() => {
            if (successDiv.parentNode) {
                successDiv.remove();
            }
        }, 3000);
    }

    /**
     * 显示错误消息
     */
    showError(message) {
        this.setState({ error: message });
        this.updateErrorDisplay();
    }

    /**
     * 处理错误
     */
    handleError(message, error) {
        console.error(message, error);
        this.showError(`${message}: ${error.message || error}`);
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        if (typeof text !== 'string') return text;

        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    cleanup() {
        // 清理DOM元素引用
        this.elements = {};

        // 调用父类清理方法
        super.cleanup();

        if (this.config.debugMode) {
            console.log('NotionDebugTools: 组件已清理');
        }
    }
}

// 注册组件
if (window.NotionRegistry) {
    window.NotionRegistry.register('notion-debug-tools', NotionDebugTools);
} else {
    console.error('NotionRegistry not found. Make sure component-registry.js is loaded first.');
}
