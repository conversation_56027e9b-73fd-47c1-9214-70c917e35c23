/**
 * Notion API Service - 统一AJAX服务层
 * 
 * 封装所有现有AJAX处理器调用，提供统一的API接口给Web Components使用
 * 基于现有的WordPress AJAX架构，保持nonce验证、权限检查和错误处理机制
 * 
 * @since 2.0.0-beta.1
 * <AUTHOR>
 */

class NotionApiService {
    constructor() {
        // 从WordPress全局变量获取配置
        this.ajaxUrl = window.notionToWp?.ajax_url || '/wp-admin/admin-ajax.php';
        this.nonce = window.notionToWp?.nonce || '';
        this.version = window.notionToWp?.version || '2.0.0';
        
        // 请求配置
        this.defaultTimeout = 30000; // 30秒超时
        this.retryAttempts = 2; // 重试次数
        
        // 验证必要配置
        if (!this.nonce) {
            console.warn('NotionApiService: 缺少nonce，AJAX请求可能失败');
        }
    }

    /**
     * 通用AJAX请求方法
     * 
     * @param {string} action - WordPress AJAX action名称
     * @param {Object} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise<Object>} 响应数据
     */
    async request(action, data = {}, options = {}) {
        const requestData = new URLSearchParams({
            action: action,
            nonce: this.nonce,
            ...data
        });

        const requestOptions = {
            method: 'POST',
            body: requestData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            timeout: options.timeout || this.defaultTimeout,
            ...options
        };

        try {
            const response = await this.fetchWithTimeout(this.ajaxUrl, requestOptions);
            
            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            
            // WordPress AJAX标准响应格式检查
            if (result.success === false) {
                throw new Error(result.data?.message || '请求失败');
            }

            return result;
            
        } catch (error) {
            console.error(`NotionApiService: ${action} 请求失败:`, error);
            throw error;
        }
    }

    /**
     * 带超时的fetch请求
     * 
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<Response>} fetch响应
     */
    async fetchWithTimeout(url, options) {
        const { timeout = this.defaultTimeout, ...fetchOptions } = options;
        
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), timeout);
        
        try {
            const response = await fetch(url, {
                ...fetchOptions,
                signal: controller.signal
            });
            clearTimeout(timeoutId);
            return response;
        } catch (error) {
            clearTimeout(timeoutId);
            if (error.name === 'AbortError') {
                throw new Error('请求超时');
            }
            throw error;
        }
    }

    // ==================== API设置相关 ====================

    /**
     * 获取统计信息
     * 对应: handle_get_stats
     */
    async getStats() {
        return this.request('notion_to_wordpress_get_stats');
    }

    /**
     * 测试Notion API连接
     * 对应: handle_test_connection
     */
    async testConnection(apiKey, databaseId) {
        return this.request('notion_to_wordpress_test_connection', {
            api_key: apiKey,
            database_id: databaseId
        });
    }

    /**
     * 手动同步
     * 对应: handle_manual_import
     */
    async manualSync(options = {}) {
        const data = {
            incremental: options.incremental !== false, // 默认增量同步
            check_deletions: options.checkDeletions !== false, // 默认检查删除
            ...options
        };
        
        return this.request('notion_to_wordpress_manual_sync', data, {
            timeout: 300000 // 5分钟超时，因为同步可能很耗时
        });
    }

    // ==================== 性能监控相关 ====================

    /**
     * 获取异步处理状态
     * 对应: handle_get_async_status
     */
    async getAsyncStatus() {
        return this.request('notion_to_wordpress_get_async_status');
    }

    /**
     * 获取队列状态
     * 对应: handle_get_queue_status
     */
    async getQueueStatus() {
        return this.request('notion_to_wordpress_get_queue_status');
    }

    /**
     * 控制异步操作
     * 对应: handle_control_async_operation
     */
    async controlAsyncOperation(operation, params = {}) {
        return this.request('notion_to_wordpress_control_async_operation', {
            operation: operation,
            ...params
        });
    }

    /**
     * 清理队列
     * 对应: handle_cleanup_queue
     */
    async cleanupQueue() {
        return this.request('notion_to_wordpress_cleanup_queue');
    }

    /**
     * 取消队列任务
     * 对应: handle_cancel_queue_task
     */
    async cancelQueueTask(taskId) {
        return this.request('notion_to_wordpress_cancel_queue_task', {
            task_id: taskId
        });
    }

    /**
     * 刷新性能统计
     * 对应: handle_refresh_performance_stats
     */
    async refreshPerformanceStats() {
        return this.request('notion_to_wordpress_refresh_performance_stats');
    }

    /**
     * 重置性能统计
     * 对应: handle_reset_performance_stats
     */
    async resetPerformanceStats() {
        return this.request('notion_to_wordpress_reset_performance_stats');
    }

    // ==================== 数据库索引相关 ====================

    /**
     * 获取索引状态
     * 对应: handle_get_index_status
     */
    async getIndexStatus() {
        return this.request('notion_to_wordpress_get_index_status');
    }

    /**
     * 创建数据库索引
     * 对应: handle_create_database_indexes
     */
    async createDatabaseIndexes() {
        return this.request('notion_to_wordpress_create_database_indexes');
    }

    /**
     * 移除数据库索引
     * 对应: handle_remove_database_indexes
     */
    async removeDatabaseIndexes() {
        return this.request('notion_to_wordpress_remove_database_indexes');
    }

    // ==================== 调试工具相关 ====================

    /**
     * 查看日志
     * 对应: handle_view_log
     */
    async viewLog(logType = 'all', limit = 100) {
        return this.request('notion_to_wordpress_view_log', {
            log_type: logType,
            limit: limit
        });
    }

    /**
     * 清除日志
     * 对应: handle_clear_logs
     */
    async clearLogs() {
        return this.request('notion_to_wordpress_clear_logs');
    }

    /**
     * 测试调试功能
     * 对应: handle_test_debug
     */
    async testDebug() {
        return this.request('notion_to_wordpress_test_debug');
    }

    // ==================== 其他功能 ====================

    /**
     * 刷新验证令牌
     * 对应: handle_refresh_verification_token
     */
    async refreshVerificationToken() {
        return this.request('notion_to_wordpress_refresh_verification_token');
    }

    /**
     * 获取智能推荐
     * 对应: handle_smart_recommendations
     */
    async getSmartRecommendations() {
        return this.request('notion_to_wordpress_get_smart_recommendations');
    }

    /**
     * 保存设置 (通过AJAX)
     * 对应: handle_save_settings_ajax
     */
    async saveSettings(settings) {
        return this.request('save_notion_settings', settings);
    }

    // ==================== 工具方法 ====================

    /**
     * 检查服务是否可用
     */
    isAvailable() {
        return !!(this.ajaxUrl && this.nonce);
    }

    /**
     * 获取错误信息的本地化文本
     */
    getErrorMessage(error) {
        const i18n = window.notionToWp?.i18n || {};
        
        if (error.message) {
            return error.message;
        }
        
        if (error.includes('timeout')) {
            return i18n.timeout_error || '请求超时';
        }
        
        if (error.includes('network')) {
            return i18n.network_error || '网络错误';
        }
        
        return i18n.error || '未知错误';
    }
}

// 导出为ES6模块（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = NotionApiService;
}

// 同时支持全局访问
if (typeof window !== 'undefined') {
    window.NotionApiService = NotionApiService;
}
